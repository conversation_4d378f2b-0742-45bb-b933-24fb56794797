import type { Driver } from "./driver";
import type { Vehicle } from "./vehicle";

// Basic assignment interface for simple use cases (e.g., payments)
export interface BasicAssignment {
  id: string;
  driverName: string;
  vehicleName: string;
  vehicleRegistration: string;
  weeklyRate: number;
  outstandingBalance: number;
}

// Comprehensive assignment interface for detailed management
export interface Assignment {
  id: string;
  driverId: string;
  driverName: string;
  driverEmail: string;
  driverPhone: string;
  vehicleId: string;
  vehicleName: string;
  vehicleRegistration: string;
  vehicleImageUrl?: string | null; // ✅ Vehicle image from database
  assignmentDate: string;
  status: "pending_setup" | "contract_uploaded" | "active" | "terminated";
  weeklyRate: number;
  initiationFee: number;
  initiationFeePaid: number;
  outstandingBalance: number;
  contractUploaded: boolean;
  contractFilePath?: string | null; // ✅ Contract file path from database
  documentsComplete: boolean;
  lastPaymentDate?: string;
  nextPaymentDue: string;
  paymentStatus: "current" | "overdue" | "pending";
  performanceRating?: number;
  notes?: string;
}

export interface AssignmentDetailsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  assignment: Assignment | null;
  mode: "view" | "edit";
  onUpdate?: (assignmentData: any) => void;
  onRecordPayment?: (assignmentId: string) => void;
  onTerminate?: (assignmentId: string, reason: string) => void;
}

export interface VehicleAssignmentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (assignmentData: any) => void;
  approvedDrivers: Driver[];
  availableVehicles: Vehicle[];
}
