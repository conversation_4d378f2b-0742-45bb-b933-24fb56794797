import type { BasicAssignment } from "./assignments";

export interface PaymentRecord {
  id: string;
  assignmentId: string;
  driverName: string;
  vehicleName: string;
  vehicleRegistration: string;
  paymentType: "weekly_lease" | "deposit" | "payout" | "debt_recovery" | "initiation_fee" | "late_fee" | "maintenance";
  amount: number;
  dueDate: string;
  paidDate?: string;
  status: "paid" | "pending" | "overdue" | "failed";
  paymentMethod?: "bank_transfer" | "cash" | "eft" | "card" | "other";
  reference?: string;
  lateFee?: number;
  notes?: string;
}

export interface PaymentRecordDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (paymentData: any) => void;
  assignments: BasicAssignment[];
  selectedAssignmentId?: string;
}
