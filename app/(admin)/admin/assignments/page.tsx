"use client";

import React, { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Search,
  Filter,
  Plus,
  User,
  Car,
  FileText,
  Edit,
  Eye,
  Settings,
  CheckCircle,
  Clock,
  AlertTriangle,
  DollarSign,
  Calendar,
  Upload,
  Download,
  Handshake,
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import VehicleAssignmentDialog from "./components/VehicleAssignmentDialog";
import AssignmentDetailsDialog from "./components/AssignmentDetailsDialog";
import VehicleHandoverDialog from "./components/VehicleHandoverDialog";
import PaymentRecordDialog from "../payments/components/PaymentRecordDialog";
import { forceBodyStyleCleanup } from "../hooks/useBodyStyleCleanup";
import type { Assignment } from "../types/assignments";
import {
  getApprovedEhailingDrivers,
  createInventoryVehicleDriverAssignment,
  getInventoryVehicleDriverAssignments,
  createVehicleDriverAssignment,
  getVehicleDriverAssignments,
  createComprehensiveVehicleAssignment,
  getHAssignments, // ✅ NEW: Query h_assignments table
} from "@/actions/admin/assignments";
import { getAvailableVehiclesInventory } from "@/actions/admin/inventory";
import { getAssignmentContractFilePath, debugAssignmentContracts } from "@/actions/admin/contract-management";

export default function AssignmentsPage() {
  const [activeTab, setActiveTab] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [isAssignmentDialogOpen, setIsAssignmentDialogOpen] = useState(false);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);
  const [isPaymentDialogOpen, setIsPaymentDialogOpen] = useState(false);
  const [selectedAssignment, setSelectedAssignment] =
    useState<Assignment | null>(null);
  const [detailsMode, setDetailsMode] = useState<"view" | "edit">("view");
  const [selectedAssignmentForPayment, setSelectedAssignmentForPayment] =
    useState<string | undefined>(undefined);
  const [isHandoverDialogOpen, setIsHandoverDialogOpen] = useState(false);
  const [selectedAssignmentForHandover, setSelectedAssignmentForHandover] =
    useState<Assignment | null>(null);

  // Real data from API
  const [approvedDrivers, setApprovedDrivers] = useState<any[]>([]);
  const [availableVehicles, setAvailableVehicles] = useState<any[]>([]);
  const [assignments, setAssignments] = useState<Assignment[]>([]);
  const [loading, setLoading] = useState(true);

  // Load data on component mount
  React.useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        const [driversResult, vehiclesResult, assignmentsResult] =
          await Promise.all([
            getApprovedEhailingDrivers(),
            getAvailableVehiclesInventory(),
            getHAssignments(), // ✅ UPDATED: Use h_assignments table query
          ]);

        if (driversResult.success && driversResult.data) {
          // Transform driver data to match UI expectations
          const transformedDrivers = driversResult.data.map((driver: any) => ({
            id: driver.applicantId.toString(),
            name: `${driver.firstName || ""} ${driver.lastName || ""}`.trim(),
            email: driver.email || "",
            phone: driver.phone || "",
            applicationId: driver.applicationId.toString(),
            approvalDate:
              driver.approvedAt instanceof Date
                ? driver.approvedAt.toISOString().split("T")[0]
                : typeof driver.approvedAt === "string"
                  ? driver.approvedAt.split("T")[0]
                  : "",
            weeklyRate: 2700, // Default - should come from listing details
            initiationFee: 7500, // Default - should come from listing details
          }));

          // ✅ Filter out drivers who are already assigned to vehicles
          const assignedDriverIds = new Set(
            assignmentsResult.success && assignmentsResult.data
              ? assignmentsResult.data
                  .filter((assignment: any) => assignment.status === "active") // ✅ Use actual status field
                  .map((assignment: any) => assignment.driverPartyId.toString())
              : []
          );

          const availableDrivers = transformedDrivers.filter(
            (driver: any) => !assignedDriverIds.has(driver.id)
          );

          setApprovedDrivers(availableDrivers);
        }

        if (vehiclesResult.success && vehiclesResult.data) {
          // Transform vehicle data to match UI expectations
          const transformedVehicles = vehiclesResult.data.map(
            (vehicle: any) => ({
              id: vehicle.vehicleId.toString(),
              make: vehicle.make || "Unknown",
              model: vehicle.model || "Unknown",
              year: vehicle.year || 0,
              color: vehicle.color || "Unknown",
              registration: vehicle.vehicleRegistration || "Unknown",
              status: "available" as const,
              location: "Unknown", // Could be derived from admin location
            })
          );
          setAvailableVehicles(transformedVehicles);
        }

        if (assignmentsResult.success && assignmentsResult.data) {
          // Debug raw assignment data
          console.log(
            "🔍 [AssignmentsPage] Raw assignment data from backend:",
            assignmentsResult.data[0]
          );

          // Transform assignment data to match UI expectations
          const transformedAssignments = assignmentsResult.data.map(
            (assignment: any) => ({
              id: assignment.assignmentId.toString(), // ✅ Use assignmentId from h_assignments
              driverId: assignment.driverPartyId.toString(),
              driverName:
                `${assignment.driverFirstName || ""} ${assignment.driverLastName || ""}`.trim(),
              driverEmail: assignment.driverEmail || "",
              driverPhone: assignment.driverPhone || "", // ✅ Now available
              vehicleId: assignment.vehicleId.toString(),
              vehicleName:
                `${assignment.vehicleMake || "Unknown"} ${assignment.vehicleModel || "Unknown"} ${assignment.vehicleYear || ""}`.trim(),
              vehicleRegistration: assignment.vehicleRegistration || "Unknown",
              vehicleImageUrl: assignment.vehicleImageUrl || null, // ✅ Vehicle image from database
              assignmentDate:
                assignment.createdAt instanceof Date
                  ? assignment.createdAt.toISOString().split("T")[0]
                  : typeof assignment.createdAt === "string"
                    ? assignment.createdAt.split("T")[0]
                    : "",
              status: assignment.status as Assignment["status"],
              // ✅ Enhanced data from database and metadata
              weeklyRate: assignment.metadata?.weeklyRate || 2700,
              initiationFee: assignment.metadata?.initiationFee || 7500,
              initiationFeePaid: assignment.metadata?.initiationFeePaid || 0,
              outstandingBalance:
                (assignment.metadata?.initiationFee || 7500) -
                (assignment.metadata?.initiationFeePaid || 0),
              contractUploaded: assignment.contractUploaded || false, // ✅ From database query
              contractFilePath: assignment.contractFilePath || null, // ✅ Contract file path
              documentsComplete: true, // Assumed since driver is approved
              nextPaymentDue: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
                .toISOString()
                .split("T")[0],
              paymentStatus: "pending" as const,
            })
          );
          setAssignments(transformedAssignments);
        }
      } catch (error) {
        console.error("Error loading assignments data:", error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800 border-green-200";
      case "contract_uploaded":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "pending_setup":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "terminated":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <CheckCircle size={12} className="mr-1" />;
      case "contract_uploaded":
        return <FileText size={12} className="mr-1" />;
      case "pending_setup":
        return <Clock size={12} className="mr-1" />;
      case "terminated":
        return <AlertTriangle size={12} className="mr-1" />;
      default:
        return <Clock size={12} className="mr-1" />;
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case "current":
        return "bg-green-100 text-green-800";
      case "overdue":
        return "bg-red-100 text-red-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const filteredAssignments = assignments.filter((assignment) => {
    const matchesSearch =
      assignment.driverName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      assignment.vehicleName
        .toLowerCase()
        .includes(searchQuery.toLowerCase()) ||
      assignment.vehicleRegistration
        .toLowerCase()
        .includes(searchQuery.toLowerCase());

    const matchesStatus =
      filterStatus === "all" || assignment.status === filterStatus;
    const matchesTab =
      activeTab === "all" ||
      (activeTab === "active" && assignment.status === "active") ||
      (activeTab === "pending" &&
        (assignment.status === "pending_setup" ||
          assignment.status === "contract_uploaded")) ||
      (activeTab === "overdue" && assignment.paymentStatus === "overdue");

    return matchesSearch && matchesStatus && matchesTab;
  });

  const getTabCount = (tab: string) => {
    switch (tab) {
      case "all":
        return assignments.length;
      case "active":
        return assignments.filter((a) => a.status === "active").length;
      case "pending":
        return assignments.filter(
          (a) =>
            a.status === "pending_setup" || a.status === "contract_uploaded"
        ).length;
      case "overdue":
        return assignments.filter((a) => a.paymentStatus === "overdue").length;
      default:
        return 0;
    }
  };

  const handleNewAssignment = () => {
    setIsAssignmentDialogOpen(true);
  };

  const handleAssignmentSubmit = async (assignmentData: any) => {
    try {
      console.log(
        "🚀 [handleAssignmentSubmit] Starting comprehensive assignment creation"
      );
      console.log("Assignment data:", assignmentData);

      // ✅ UPDATED: Use comprehensive assignment creation
      const result = await createComprehensiveVehicleAssignment(assignmentData);

      if (result.success) {
        console.log(
          "✅ Comprehensive assignment created successfully:",
          result.data
        );
        console.log("   Assignment ID:", result.data?.assignmentId);
        console.log("   Group ID:", result.data?.groupId);
        console.log("   Contract ID:", result.data?.contractId);
        console.log("   Initiation Fee ID:", result.data?.initiationFeeId);

        toast.success("Vehicle assignment created successfully!");

        // Reload assignments data
        const assignmentsResult = await getHAssignments(); // ✅ Use h_assignments query
        if (assignmentsResult.success && assignmentsResult.data) {
          // Debug raw assignment data after creation
          console.log(
            "🔍 [AssignmentsPage] Raw assignment data after creation:",
            assignmentsResult.data[0]
          );

          const transformedAssignments = assignmentsResult.data.map(
            (assignment: any) => ({
              id: assignment.assignmentId.toString(), // ✅ FIXED: Use assignmentId consistently
              driverId: assignment.driverPartyId.toString(),
              driverName:
                `${assignment.driverFirstName || ""} ${assignment.driverLastName || ""}`.trim(),
              driverEmail: assignment.driverEmail || "",
              driverPhone: assignment.driverPhone || "", // ✅ Now available
              vehicleId: assignment.vehicleId.toString(),
              vehicleName:
                `${assignment.vehicleMake || "Unknown"} ${assignment.vehicleModel || "Unknown"} ${assignment.vehicleYear || ""}`.trim(),
              vehicleRegistration: assignment.vehicleRegistration || "Unknown", // ✅ Fixed
              vehicleImageUrl: assignment.vehicleImageUrl || null, // ✅ Vehicle image from database
              assignmentDate:
                assignment.createdAt instanceof Date
                  ? assignment.createdAt.toISOString().split("T")[0]
                  : typeof assignment.createdAt === "string"
                    ? assignment.createdAt.split("T")[0]
                    : "",
              status: assignment.status as Assignment["status"], // ✅ Use actual status
              // ✅ Enhanced data from database and metadata
              weeklyRate: assignment.metadata?.weeklyRate || 2700,
              initiationFee: assignment.metadata?.initiationFee || 7500,
              initiationFeePaid: assignment.metadata?.initiationFeePaid || 0,
              outstandingBalance:
                (assignment.metadata?.initiationFee || 7500) -
                (assignment.metadata?.initiationFeePaid || 0),
              contractUploaded: assignment.contractUploaded || false, // ✅ From database query
              contractFilePath: assignment.contractFilePath || null, // ✅ Contract file path
              documentsComplete: true,
              nextPaymentDue: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
                .toISOString()
                .split("T")[0],
              paymentStatus: "pending" as const,
            })
          );
          setAssignments(transformedAssignments);

          // ✅ Refresh the available drivers list to remove the newly assigned driver
          const driversResult = await getApprovedEhailingDrivers();
          if (driversResult.success && driversResult.data) {
            const transformedDrivers = driversResult.data.map(
              (driver: any) => ({
                id: driver.applicantId.toString(),
                name: `${driver.firstName || ""} ${driver.lastName || ""}`.trim(),
                email: driver.email || "",
                phone: driver.phone || "",
                applicationId: driver.applicationId.toString(),
                approvalDate:
                  driver.approvedAt instanceof Date
                    ? driver.approvedAt.toISOString().split("T")[0]
                    : typeof driver.approvedAt === "string"
                      ? driver.approvedAt.split("T")[0]
                      : "",
                weeklyRate: 2700,
                initiationFee: 7500,
              })
            );

            // Filter out assigned drivers using the updated assignments
            const assignedDriverIds = new Set(
              transformedAssignments
                .filter((assignment: any) => assignment.status === "active")
                .map((assignment: any) => assignment.driverId)
            );

            const availableDrivers = transformedDrivers.filter(
              (driver: any) => !assignedDriverIds.has(driver.id)
            );

            setApprovedDrivers(availableDrivers);
          }
        }

        handleCloseAssignmentDialog();
        toast.success("Vehicle assignment created successfully!");
      } else {
        console.error("Failed to create assignment:", result.error);
        toast.error(result.error || "Failed to create assignment");
      }
    } catch (error) {
      console.error("Error creating assignment:", error);
      toast.error("An error occurred while creating the assignment");
    }
  };

  const handleCloseAssignmentDialog = () => {
    setIsAssignmentDialogOpen(false);
    // Force cleanup of body styles to prevent freezing
    setTimeout(() => {
      forceBodyStyleCleanup();
    }, 100);
  };

  const handleViewAssignment = (assignment: Assignment) => {
    // Small delay to ensure dropdown closes before modal opens
    setTimeout(() => {
      setSelectedAssignment(assignment);
      setDetailsMode("view");
      setIsDetailsDialogOpen(true);
    }, 100);
  };

  const handleEditAssignment = (assignment: Assignment) => {
    // Small delay to ensure dropdown closes before modal opens
    setTimeout(() => {
      setSelectedAssignment(assignment);
      setDetailsMode("edit");
      setIsDetailsDialogOpen(true);
    }, 100);
  };

  const handleCloseDetailsDialog = () => {
    setIsDetailsDialogOpen(false);
    // Don't reset selectedAssignment immediately to avoid state conflicts
    setTimeout(() => {
      setSelectedAssignment(null);
      // Force cleanup of body styles to prevent freezing
      forceBodyStyleCleanup();
    }, 150);
  };

  const handleUpdateAssignment = (assignmentData: any) => {
    // TODO: Implement API call to update assignment
    console.log("Update assignment:", assignmentData);

    // Only close dialog after successful update
    setTimeout(() => {
      handleCloseDetailsDialog();
      // You could also show a success toast here
    }, 100);
  };

  const handleRecordPaymentFromDetails = (assignmentId: string) => {
    // Close details dialog and open payment dialog
    handleCloseDetailsDialog();
    setSelectedAssignmentForPayment(assignmentId);
    setIsPaymentDialogOpen(true);
  };

  const handleRecordPaymentFromTable = (assignmentId: string) => {
    setSelectedAssignmentForPayment(assignmentId);
    setIsPaymentDialogOpen(true);
  };

  const handlePaymentSubmit = (paymentData: any) => {
    // TODO: Implement API call to record payment
    console.log("Payment data:", paymentData);
    setIsPaymentDialogOpen(false);
    setSelectedAssignmentForPayment(undefined);
  };

  const handleClosePaymentDialog = () => {
    setIsPaymentDialogOpen(false);
    setSelectedAssignmentForPayment(undefined);
  };

  const handleTerminateAssignment = (assignmentId: string, reason: string) => {
    // TODO: Implement API call to terminate assignment
    console.log("Terminate assignment:", assignmentId, "Reason:", reason);
    handleCloseDetailsDialog();
  };

  const handleViewContract = async (assignment: Assignment) => {
    if (!assignment.contractUploaded) {
      alert("No contract found for this assignment.");
      return;
    }

    try {
      console.log("🔍 [handleViewContract] Starting for assignment:", assignment.id);
      console.log("🔍 [handleViewContract] Assignment object:", assignment);

      // First, let's debug what contracts exist for this assignment
      const debugResult = await debugAssignmentContracts(parseInt(assignment.id));
      console.log("🔍 [handleViewContract] Debug result:", debugResult);

      // Get the contract file path from server
      const result = await getAssignmentContractFilePath(parseInt(assignment.id));
      console.log("🔍 [handleViewContract] Contract file path result:", result);

      if (result.success && result.data) {
        console.log("🔍 [handleViewContract] Contract file path:", result.data.filePath);

        // Generate signed URL on client side (like applications page)
        const { generateDocumentUrl } = await import("@/lib/utils");
        const signedUrl = await generateDocumentUrl(result.data.filePath);
        console.log("✅ [handleViewContract] Generated signed URL:", signedUrl);

        window.open(signedUrl, "_blank");
      } else {
        console.error("❌ [handleViewContract] Failed to get contract file path:", result.error);
        alert(`Failed to open contract: ${result.error}`);
      }
    } catch (error) {
      console.error("❌ [handleViewContract] Error viewing contract:", error);
      alert("Failed to open contract. Please try again.");
    }
  };

  const handleInitiateHandover = (assignment: Assignment) => {
    // Small delay to ensure dropdown closes before modal opens
    setTimeout(() => {
      setSelectedAssignmentForHandover(assignment);
      setIsHandoverDialogOpen(true);
    }, 100);
  };

  const handleHandoverSubmit = (handoverData: any) => {
    // TODO: Implement API call to initiate handover
    console.log("Handover data:", handoverData);
    handleCloseHandoverDialog();
  };

  const handleCloseHandoverDialog = () => {
    setIsHandoverDialogOpen(false);
    setSelectedAssignmentForHandover(null);
    // Force cleanup of body styles to prevent freezing
    setTimeout(() => {
      forceBodyStyleCleanup();
    }, 100);
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">
          Vehicle Assignments
        </h1>
        <Button
          onClick={handleNewAssignment}
          className="bg-[#009639] hover:bg-[#007A2F] text-white flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          New Assignment
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Total Assignments</p>
                <p className="text-2xl font-bold mt-1">{assignments.length}</p>
              </div>
              <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
                <User className="text-blue-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Active</p>
                <p className="text-2xl font-bold mt-1">
                  {assignments.filter((a) => a.status === "active").length}
                </p>
              </div>
              <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
                <CheckCircle className="text-green-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Pending Setup</p>
                <p className="text-2xl font-bold mt-1">
                  {
                    assignments.filter(
                      (a) =>
                        a.status === "pending_setup" ||
                        a.status === "contract_uploaded"
                    ).length
                  }
                </p>
              </div>
              <div className="w-12 h-12 rounded-full bg-yellow-100 flex items-center justify-center">
                <Clock className="text-yellow-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Outstanding Balance</p>
                <p className="text-2xl font-bold mt-1">
                  R
                  {assignments
                    .reduce((sum, a) => sum + a.outstandingBalance, 0)
                    .toLocaleString()}
                </p>
              </div>
              <div className="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center">
                <DollarSign className="text-red-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Card>
        <CardHeader>
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            {/* Tabs */}
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList>
                <TabsTrigger value="all" className="flex items-center gap-2">
                  All ({getTabCount("all")})
                </TabsTrigger>
                <TabsTrigger value="active" className="flex items-center gap-2">
                  <CheckCircle size={16} />
                  Active ({getTabCount("active")})
                </TabsTrigger>
                <TabsTrigger
                  value="pending"
                  className="flex items-center gap-2"
                >
                  <Clock size={16} />
                  Pending ({getTabCount("pending")})
                </TabsTrigger>

              </TabsList>
            </Tabs>

            {/* Search and Filters */}
            <div className="flex gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search assignments..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 w-[250px]"
                />
              </div>
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="px-3 py-2 border border-gray-200 rounded-md text-sm"
                aria-label="Filter by assignment status"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="contract_uploaded">Contract Uploaded</option>
                <option value="pending_setup">Pending Setup</option>
                <option value="terminated">Terminated</option>
              </select>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Driver</TableHead>
                  <TableHead>Vehicle</TableHead>
                  <TableHead>Assignment Date</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-8">
                      <div className="flex items-center justify-center">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#009639] mr-3"></div>
                        Loading assignments...
                      </div>
                    </TableCell>
                  </TableRow>
                ) : filteredAssignments.length === 0 ? (
                  <TableRow>
                    <TableCell
                      colSpan={5}
                      className="text-center py-8 text-gray-500"
                    >
                      No assignments found
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredAssignments.map((assignment) => (
                    <TableRow key={assignment.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">
                            {assignment.driverName}
                          </div>
                          <div className="text-sm text-gray-500">
                            {assignment.driverEmail}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">
                            {assignment.vehicleName}
                          </div>
                          <div className="text-sm text-gray-500">
                            {assignment.vehicleRegistration}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center text-sm">
                          <Calendar size={14} className="mr-1 text-gray-400" />
                          {new Date(
                            assignment.assignmentDate
                          ).toLocaleDateString()}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant="outline"
                          className={getStatusColor(assignment.status)}
                        >
                          {getStatusIcon(assignment.status)}
                          {assignment.status
                            .replace("_", " ")
                            .replace(/\b\w/g, (l) => l.toUpperCase())}
                        </Badge>
                      </TableCell>

                      <TableCell className="text-right">
                        <div className="flex items-center justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.preventDefault();
                              handleViewAssignment(assignment);
                            }}
                            className="h-8 w-8 p-0"
                          >
                            <Eye size={16} className="text-gray-600" />
                          </Button>
                          {assignment.contractUploaded && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.preventDefault();
                                handleViewContract(assignment);
                              }}
                              className="h-8 w-8 p-0"
                              title="View Contract"
                            >
                              <FileText size={16} className="text-green-600" />
                            </Button>
                          )}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.preventDefault();
                              handleEditAssignment(assignment);
                            }}
                            className="h-8 w-8 p-0"
                          >
                            <Edit size={16} className="text-gray-600" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Vehicle Assignment Dialog */}
      <VehicleAssignmentDialog
        isOpen={isAssignmentDialogOpen}
        onClose={handleCloseAssignmentDialog}
        onConfirm={handleAssignmentSubmit}
        approvedDrivers={approvedDrivers}
        availableVehicles={availableVehicles}
      />

      {/* Assignment Details Dialog */}
      <AssignmentDetailsDialog
        isOpen={isDetailsDialogOpen}
        onClose={handleCloseDetailsDialog}
        assignment={selectedAssignment}
        mode={detailsMode}
        onUpdate={handleUpdateAssignment}
        onRecordPayment={handleRecordPaymentFromDetails}
        onTerminate={handleTerminateAssignment}
      />

      {/* Payment Record Dialog */}
      <PaymentRecordDialog
        isOpen={isPaymentDialogOpen}
        onClose={handleClosePaymentDialog}
        onConfirm={handlePaymentSubmit}
        assignments={assignments.map((assignment) => ({
          id: assignment.id,
          driverName: assignment.driverName,
          vehicleName: assignment.vehicleName,
          vehicleRegistration: assignment.vehicleRegistration,
          weeklyRate: assignment.weeklyRate,
          outstandingBalance: assignment.outstandingBalance,
        }))}
        selectedAssignmentId={selectedAssignmentForPayment}
      />

      {/* Vehicle Handover Dialog */}
      <VehicleHandoverDialog
        isOpen={isHandoverDialogOpen}
        onClose={handleCloseHandoverDialog}
        onConfirm={handleHandoverSubmit}
        assignment={selectedAssignmentForHandover}
      />
    </div>
  );
}
