"use client";

import React, { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Search,
  Filter,
  Plus,
  DollarSign,
  User,
  Car,
  Calendar,
  CheckCircle,
  Clock,
  AlertTriangle,
  XCircle,
  TrendingUp,
  TrendingDown,
  Eye,
  Edit,
  Settings,
  Download,
  Send,
  BarChart3,
  CreditCard,
  RefreshCw,
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import PaymentRecordDialog from "./components/PaymentRecordDialog";
import DriverPaymentHistoryDialog from "./components/DriverPaymentHistoryDialog";
import WeeklyEarningsManagement from "./components/WeeklyEarningsManagement";
import DriverPayoutManagement from "./components/DriverPayoutManagement";
import DebtManagementDashboard from "./components/DebtManagementDashboard";
import CreateAssignmentForm from "./components/CreateAssignmentForm";
import type { PaymentRecord } from "../types/payments";
import type { BasicAssignment } from "../types/assignments";

// Import our payment actions
import {
  getAllPaymentRecordsAction,
  getBasicAssignmentsAction,
  refreshPaymentDataAction,
  testAssignmentLoadingAction,
} from "@/actions/admin/payments";
import {
  createVehicleDriverAssignment,
} from "@/actions/admin/assignments";
import { toast } from "sonner";

export default function PaymentsPage() {
  const [activeTab, setActiveTab] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [filterType, setFilterType] = useState<string>("all");
  const [isPaymentDialogOpen, setIsPaymentDialogOpen] = useState(false);
  const [isDriverHistoryDialogOpen, setIsDriverHistoryDialogOpen] =
    useState(false);
  const [selectedDriverForHistory, setSelectedDriverForHistory] = useState<
    string | null
  >(null);

  // Real assignments data loaded from database
  const [assignments, setAssignments] = useState<BasicAssignment[]>([]);
  const [assignmentsLoading, setAssignmentsLoading] = useState(true);

  // Real payments data loaded from database
  const [payments, setPayments] = useState<PaymentRecord[]>([]);
  const [paymentsLoading, setPaymentsLoading] = useState(true);

  // Load data on component mount
  useEffect(() => {
    loadPaymentData();
    loadAssignmentData();
  }, []);

  const loadPaymentData = async () => {
    try {
      setPaymentsLoading(true);
      const result = await getAllPaymentRecordsAction();

      if (result.success && result.data) {
        setPayments(result.data);
      } else {
        toast.error(result.error || "Failed to load payment records");
      }
    } catch (error) {
      console.error("Error loading payment data:", error);
      toast.error("Failed to load payment records");
    } finally {
      setPaymentsLoading(false);
    }
  };

  const loadAssignmentData = async () => {
    try {
      setAssignmentsLoading(true);
      const result = await getBasicAssignmentsAction();

      if (result.success && result.data) {
        setAssignments(result.data);
      } else {
        toast.error(result.error || "Failed to load assignments");
      }
    } catch (error) {
      console.error("Error loading assignment data:", error);
      toast.error("Failed to load assignments");
    } finally {
      setAssignmentsLoading(false);
    }
  };

  const handleRefreshData = async () => {
    try {
      await refreshPaymentDataAction();
      await loadPaymentData();
      await loadAssignmentData();
      toast.success("Payment data refreshed successfully");
    } catch (error) {
      console.error("Error refreshing data:", error);
      toast.error("Failed to refresh payment data");
    }
  };

  const handleTestAssignments = async () => {
    try {
      const result = await testAssignmentLoadingAction();
      console.log("🧪 Test assignments result:", result);

      if (result.success) {
        const assignmentCount = result.data?.newAssignmentsCount || 0;
        toast.success(`Found ${assignmentCount} assignments. Check console for details.`);

        // Show helpful info for Phase 3 testing
        if (result.data?.newAssignmentsData && result.data.newAssignmentsData.length > 0) {
          const existingAssignment = result.data.newAssignmentsData[0];
          console.log("💡 For Phase 3 testing, you can use these existing IDs:");
          console.log(`   Driver Party ID: ${existingAssignment.driverPartyId || 'Unknown'}`);
          console.log(`   Vehicle ID: ${existingAssignment.vehicleId || 'Unknown'}`);
          console.log("   Or find other approved drivers and available vehicles in your database");
        }
      } else {
        toast.error(result.error || "Failed to test assignments");
      }
    } catch (error) {
      console.error("Error testing assignments:", error);
      toast.error("Failed to test assignments");
    }
  };



  // Calculate stats
  const getTotalAmount = (status: string) => {
    if (status === "all") {
      return payments.reduce((sum, payment) => sum + payment.amount, 0);
    }
    return payments
      .filter((payment) => payment.status === status)
      .reduce((sum, payment) => sum + payment.amount, 0);
  };

  const getTabCount = (tab: string) => {
    if (tab === "all") return payments.length;
    return payments.filter((payment) => payment.status === tab).length;
  };

  // Utility functions
  const isOverdue = (dueDate: string, status: string) => {
    if (status === "paid") return false;
    return new Date(dueDate) < new Date();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const getStatusBadge = (status: string) => {
    const styles = {
      paid: "bg-green-100 text-green-800 border-green-200",
      pending: "bg-yellow-100 text-yellow-800 border-yellow-200",
      overdue: "bg-red-100 text-red-800 border-red-200",
      partial: "bg-blue-100 text-blue-800 border-blue-200",
    };

    return (
      <Badge variant="outline" className={styles[status as keyof typeof styles]}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const getPaymentTypeLabel = (type: string) => {
    const labels = {
      weekly_lease: "Weekly Lease",
      initiation_fee: "Initiation Fee",
      late_fee: "Late Fee",
      maintenance: "Maintenance",
    };
    return labels[type as keyof typeof labels] || type;
  };

  // Filter payments based on active tab and search
  const filteredPayments = payments.filter((payment) => {
    // Tab filter
    let tabMatch = true;
    if (activeTab !== "all") {
      tabMatch = payment.status === activeTab;
    }

    // Search filter
    const searchMatch =
      payment.driverName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      payment.vehicleName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      payment.vehicleRegistration
        .toLowerCase()
        .includes(searchQuery.toLowerCase());

    // Type filter
    const typeMatch =
      filterType === "all" || payment.paymentType === filterType;

    // Status filter
    const statusMatch =
      filterStatus === "all" || payment.status === filterStatus;

    return tabMatch && searchMatch && typeMatch && statusMatch;
  });

  // Event handlers
  const handleRecordPayment = () => {
    setIsPaymentDialogOpen(true);
  };

  const handleViewDriverHistory = (driverName: string) => {
    setSelectedDriverForHistory(driverName);
    setIsDriverHistoryDialogOpen(true);
  };

  const handleMarkPaid = (payment: PaymentRecord) => {
    // TODO: Implement mark as paid
    console.log("Mark as paid:", payment);
  };

  const handleSendReminder = (payment: PaymentRecord) => {
    // TODO: Implement send reminder
    console.log("Send reminder:", payment);
  };

  const handleGenerateInvoice = (payment: PaymentRecord) => {
    // TODO: Implement generate invoice
    console.log("Generate invoice:", payment);
  };

  const handleApplyLateFee = (payment: PaymentRecord) => {
    // TODO: Implement apply late fee
    console.log("Apply late fee:", payment);
  };



  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Comprehensive Financial Management</h1>
          <p className="text-gray-600 mt-1">
            Complete financial ecosystem: payments, earnings, payouts, and debt management
          </p>
        </div>
        <div className="flex gap-3">
          <CreateAssignmentForm onAssignmentCreated={loadAssignmentData} />
          <Button
            variant="outline"
            onClick={handleTestAssignments}
            className="flex items-center gap-2 bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100"
          >
            🧪 Test Assignments
          </Button>
          <Button
            variant="outline"
            onClick={handleRefreshData}
            disabled={paymentsLoading || assignmentsLoading}
            className="flex items-center gap-2"
          >
            <RefreshCw size={16} className={paymentsLoading || assignmentsLoading ? "animate-spin" : ""} />
            Refresh Data
          </Button>
          <Button variant="outline" className="flex items-center gap-2">
            <Download size={16} />
            Export Report
          </Button>
          <Button
            onClick={handleRecordPayment}
            className="bg-[#009639] hover:bg-[#007A2F] text-white flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            Record Payment
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Total Outstanding</p>
                <p className="text-2xl font-bold mt-1">
                  R{getTotalAmount("overdue").toLocaleString()}
                </p>
              </div>
              <div className="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center">
                <TrendingDown className="text-red-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Pending Payments</p>
                <p className="text-2xl font-bold mt-1">
                  R{getTotalAmount("pending").toLocaleString()}
                </p>
              </div>
              <div className="w-12 h-12 rounded-full bg-yellow-100 flex items-center justify-center">
                <Clock className="text-yellow-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Paid This Month</p>
                <p className="text-2xl font-bold mt-1">
                  R{getTotalAmount("paid").toLocaleString()}
                </p>
              </div>
              <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
                <TrendingUp className="text-green-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Late Fees</p>
                <p className="text-2xl font-bold mt-1">
                  R
                  {payments
                    .reduce((sum, p) => sum + (p.lateFee || 0), 0)
                    .toLocaleString()}
                </p>
              </div>
              <div className="w-12 h-12 rounded-full bg-orange-100 flex items-center justify-center">
                <AlertTriangle className="text-orange-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content with Enhanced Tabs */}
      <Card>
        <CardHeader>
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            {/* Enhanced Tabs with Weekly Earnings and Driver Payouts */}
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList>
                <TabsTrigger value="all" className="flex items-center gap-2">
                  All Payments ({getTabCount("all")})
                </TabsTrigger>
                <TabsTrigger
                  value="pending"
                  className="flex items-center gap-2"
                >
                  <Clock size={16} />
                  Pending ({getTabCount("pending")})
                </TabsTrigger>
                <TabsTrigger
                  value="overdue"
                  className="flex items-center gap-2"
                >
                  <AlertTriangle size={16} />
                  Overdue ({getTabCount("overdue")})
                </TabsTrigger>
                <TabsTrigger value="paid" className="flex items-center gap-2">
                  <CheckCircle size={16} />
                  Paid ({getTabCount("paid")})
                </TabsTrigger>
                <TabsTrigger value="earnings" className="flex items-center gap-2">
                  <BarChart3 size={16} />
                  Weekly Earnings
                </TabsTrigger>
                <TabsTrigger value="payouts" className="flex items-center gap-2">
                  <CreditCard size={16} />
                  Driver Payouts
                </TabsTrigger>
                <TabsTrigger value="debts" className="flex items-center gap-2">
                  <AlertTriangle size={16} />
                  Debt Management
                </TabsTrigger>
              </TabsList>
            </Tabs>

            {/* Search and Filters - Only show for payment tabs */}
            {activeTab !== "earnings" && activeTab !== "payouts" && activeTab !== "debts" && (
              <div className="flex gap-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search payments..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 w-[250px]"
                  />
                </div>
                <select
                  value={filterType}
                  onChange={(e) => setFilterType(e.target.value)}
                  className="px-3 py-2 border border-gray-200 rounded-md text-sm"
                  aria-label="Filter by payment type"
                >
                  <option value="all">All Types</option>
                  <option value="weekly_lease">Weekly Lease</option>
                  <option value="initiation_fee">Initiation Fee</option>
                  <option value="late_fee">Late Fee</option>
                  <option value="maintenance">Maintenance</option>
                </select>
                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className="px-3 py-2 border border-gray-200 rounded-md text-sm"
                  aria-label="Filter by payment status"
                >
                  <option value="all">All Status</option>
                  <option value="paid">Paid</option>
                  <option value="pending">Pending</option>
                  <option value="overdue">Overdue</option>
                  <option value="partial">Partial</option>
                </select>
              </div>
            )}
          </div>
        </CardHeader>
        
        <CardContent>
          {/* Tab Content */}
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsContent value="all" className="m-0">
              <PaymentTable
                payments={filteredPayments}
                loading={paymentsLoading}
                onViewHistory={handleViewDriverHistory}
                onMarkPaid={handleMarkPaid}
                onSendReminder={handleSendReminder}
                onGenerateInvoice={handleGenerateInvoice}
                onApplyLateFee={handleApplyLateFee}
                getStatusBadge={getStatusBadge}
                getPaymentTypeLabel={getPaymentTypeLabel}
                formatDate={formatDate}
                isOverdue={isOverdue}
              />
            </TabsContent>
            
            <TabsContent value="pending" className="m-0">
              <PaymentTable
                payments={filteredPayments}
                loading={paymentsLoading}
                onViewHistory={handleViewDriverHistory}
                onMarkPaid={handleMarkPaid}
                onSendReminder={handleSendReminder}
                onGenerateInvoice={handleGenerateInvoice}
                onApplyLateFee={handleApplyLateFee}
                getStatusBadge={getStatusBadge}
                getPaymentTypeLabel={getPaymentTypeLabel}
                formatDate={formatDate}
                isOverdue={isOverdue}
              />
            </TabsContent>

            <TabsContent value="overdue" className="m-0">
              <PaymentTable
                payments={filteredPayments}
                loading={paymentsLoading}
                onViewHistory={handleViewDriverHistory}
                onMarkPaid={handleMarkPaid}
                onSendReminder={handleSendReminder}
                onGenerateInvoice={handleGenerateInvoice}
                onApplyLateFee={handleApplyLateFee}
                getStatusBadge={getStatusBadge}
                getPaymentTypeLabel={getPaymentTypeLabel}
                formatDate={formatDate}
                isOverdue={isOverdue}
              />
            </TabsContent>

            <TabsContent value="paid" className="m-0">
              <PaymentTable
                payments={filteredPayments}
                loading={paymentsLoading}
                onViewHistory={handleViewDriverHistory}
                onMarkPaid={handleMarkPaid}
                onSendReminder={handleSendReminder}
                onGenerateInvoice={handleGenerateInvoice}
                onApplyLateFee={handleApplyLateFee}
                getStatusBadge={getStatusBadge}
                getPaymentTypeLabel={getPaymentTypeLabel}
                formatDate={formatDate}
                isOverdue={isOverdue}
              />
            </TabsContent>
            
            {/* Weekly Earnings Tab */}
            <TabsContent value="earnings" className="m-0">
              <WeeklyEarningsManagement 
                assignments={assignments}
                onRefresh={handleRefreshData}
              />
            </TabsContent>
            
            {/* NEW: Driver Payouts Tab */}
            <TabsContent value="payouts" className="m-0">
              <DriverPayoutManagement 
                assignments={assignments}
                onRefresh={handleRefreshData}
              />
            </TabsContent>
            
            {/* NEW: Debt Management Tab */}
            <TabsContent value="debts" className="m-0">
              <DebtManagementDashboard 
                assignments={assignments}
                onRefresh={handleRefreshData}
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Dialogs */}
      <PaymentRecordDialog
        isOpen={isPaymentDialogOpen}
        onClose={() => setIsPaymentDialogOpen(false)}
        onConfirm={(paymentData) => {
          console.log("Payment recorded:", paymentData);
          setIsPaymentDialogOpen(false);
        }}
        assignments={assignments}
      />

      <DriverPaymentHistoryDialog
        isOpen={isDriverHistoryDialogOpen}
        onClose={() => setIsDriverHistoryDialogOpen(false)}
        driverInfo={selectedDriverForHistory ? {
          id: "drv-001",
          name: selectedDriverForHistory,
          email: "<EMAIL>",
          phone: "+27 82 123 4567",
          vehicleName: assignments.find(a => a.driverName === selectedDriverForHistory)?.vehicleName || "Unknown",
          vehicleRegistration: assignments.find(a => a.driverName === selectedDriverForHistory)?.vehicleRegistration || "Unknown",
          assignmentDate: "2024-01-15",
          weeklyRate: assignments.find(a => a.driverName === selectedDriverForHistory)?.weeklyRate || 0,
          totalPaid: 0,
          outstandingBalance: assignments.find(a => a.driverName === selectedDriverForHistory)?.outstandingBalance || 0,
          paymentScore: 85,
          onTimePayments: 5,
          totalPayments: 6,
        } : null}
        paymentHistory={payments.filter(
          (p) => p.driverName === selectedDriverForHistory
        )}
      />
    </div>
  );
}

// Payment Table Component
interface PaymentTableProps {
  payments: PaymentRecord[];
  loading: boolean;
  onViewHistory: (driverName: string) => void;
  onMarkPaid: (payment: PaymentRecord) => void;
  onSendReminder: (payment: PaymentRecord) => void;
  onGenerateInvoice: (payment: PaymentRecord) => void;
  onApplyLateFee: (payment: PaymentRecord) => void;
  getStatusBadge: (status: string) => JSX.Element;
  getPaymentTypeLabel: (type: string) => string;
  formatDate: (date: string) => string;
  isOverdue: (dueDate: string, status: string) => boolean;
}

function PaymentTable({
  payments,
  loading,
  onViewHistory,
  onMarkPaid,
  onSendReminder,
  onGenerateInvoice,
  onApplyLateFee,
  getStatusBadge,
  getPaymentTypeLabel,
  formatDate,
  isOverdue,
}: PaymentTableProps) {
  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Driver & Vehicle</TableHead>
            <TableHead>Payment Type</TableHead>
            <TableHead>Amount</TableHead>
            <TableHead>Due Date</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Payment Details</TableHead>
            <TableHead>Notes</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {loading ? (
            <TableRow>
              <TableCell colSpan={8} className="text-center py-8">
                <div className="flex items-center justify-center gap-2">
                  <RefreshCw className="h-4 w-4 animate-spin" />
                  Loading payment records...
                </div>
              </TableCell>
            </TableRow>
          ) : payments.length === 0 ? (
            <TableRow>
              <TableCell colSpan={8} className="text-center py-8 text-gray-500">
                No payment records found. Data will appear here once payments are recorded in the system.
              </TableCell>
            </TableRow>
          ) : (
            payments.map((payment) => (
            <TableRow
              key={payment.id}
              className={
                isOverdue(payment.dueDate, payment.status)
                  ? "bg-red-50"
                  : ""
              }
            >
              <TableCell>
                <div>
                  <div className="font-medium flex items-center gap-2">
                    <User size={14} className="text-gray-400" />
                    <button
                      onClick={() => onViewHistory(payment.driverName)}
                      className="text-blue-600 hover:text-blue-800 hover:underline"
                    >
                      {payment.driverName}
                    </button>
                  </div>
                  <div className="text-sm text-gray-500 flex items-center gap-2 mt-1">
                    <Car size={12} className="text-gray-400" />
                    {payment.vehicleName}
                    <span className="text-gray-400">•</span>
                    {payment.vehicleRegistration}
                  </div>
                </div>
              </TableCell>
              <TableCell>
                <Badge variant="outline">
                  {getPaymentTypeLabel(payment.paymentType)}
                </Badge>
              </TableCell>
              <TableCell>
                <div className="font-medium">R{payment.amount.toLocaleString()}</div>
                {payment.lateFee && (
                  <div className="text-sm text-red-600">
                    + R{payment.lateFee} late fee
                  </div>
                )}
              </TableCell>
              <TableCell>
                <div className="flex items-center gap-2">
                  <Calendar size={14} className="text-gray-400" />
                  <span
                    className={
                      isOverdue(payment.dueDate, payment.status)
                        ? "text-red-600 font-medium"
                        : ""
                    }
                  >
                    {formatDate(payment.dueDate)}
                  </span>
                </div>
              </TableCell>
              <TableCell>{getStatusBadge(payment.status)}</TableCell>
              <TableCell>
                {payment.paidDate && (
                  <div className="text-sm">
                    <div className="text-gray-600">
                      Paid: {formatDate(payment.paidDate)}
                    </div>
                    {payment.paymentMethod && (
                      <div className="text-gray-500 capitalize">
                        {payment.paymentMethod.replace("_", " ")}
                      </div>
                    )}
                    {payment.reference && (
                      <div className="text-gray-500 font-mono text-xs">
                        {payment.reference}
                      </div>
                    )}
                  </div>
                )}
              </TableCell>
              <TableCell>
                <div className="text-sm text-gray-600 max-w-[200px] truncate">
                  {payment.notes}
                </div>
              </TableCell>
              <TableCell className="text-right">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <Settings size={16} />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    {payment.status !== "paid" && (
                      <>
                        <DropdownMenuItem
                          onClick={() => onMarkPaid(payment)}
                        >
                          <CheckCircle className="mr-2 h-4 w-4" />
                          Mark as Paid
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => onSendReminder(payment)}
                        >
                          <Send className="mr-2 h-4 w-4" />
                          Send Reminder
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                      </>
                    )}
                    <DropdownMenuItem
                      onClick={() => onGenerateInvoice(payment)}
                    >
                      <Download className="mr-2 h-4 w-4" />
                      Generate Invoice
                    </DropdownMenuItem>
                    {payment.status === "overdue" && !payment.lateFee && (
                      <>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => onApplyLateFee(payment)}
                          className="text-red-600"
                        >
                          <AlertTriangle className="mr-2 h-4 w-4" />
                          Apply Late Fee
                        </DropdownMenuItem>
                      </>
                    )}
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      onClick={() => onViewHistory(payment.driverName)}
                    >
                      <Eye className="mr-2 h-4 w-4" />
                      View History
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );
}
