"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Plus } from "lucide-react";
import { toast } from "sonner";
import { createVehicleDriverAssignment } from "@/actions/admin/assignments";

interface CreateAssignmentFormProps {
  onAssignmentCreated?: () => void;
}

export default function CreateAssignmentForm({ onAssignmentCreated }: CreateAssignmentFormProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    driverPartyId: "",
    vehicleId: "",
    weeklyRate: "2800",
    initiationFee: "3000",
    notes: "",
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.driverPartyId || !formData.vehicleId || !formData.weeklyRate) {
      toast.error("Please fill in all required fields");
      return;
    }

    setLoading(true);
    
    try {
      console.log("🚀 Testing Phase 3: Creating assignment via group workflow...");

      const result = await createVehicleDriverAssignment(
        parseInt(formData.driverPartyId),
        parseInt(formData.vehicleId)
      );

      console.log("🚀 Phase 3 workflow result:", result);

      if (result.success) {
        toast.success("Phase 3 test successful! Assignment created via group workflow.");
        console.log("✅ Created group:", result.data?.groupId);
        console.log("✅ Assignment details:", result.data?.assignment);

        setOpen(false);
        setFormData({
          driverPartyId: "",
          vehicleId: "",
          weeklyRate: "2800",
          initiationFee: "3000",
          notes: "",
        });
        onAssignmentCreated?.();
      } else {
        toast.error(result.error || "Failed to create assignment");
        console.error("❌ Phase 3 workflow failed:", result.error);
      }
    } catch (error) {
      console.error("Error in Phase 3 workflow:", error);
      toast.error("Failed to create assignment");
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button className="bg-[#009639] hover:bg-[#007A2F] text-white flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Test Phase 3 Workflow
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Test Phase 3: Complete Assignment Workflow</DialogTitle>
          <DialogDescription>
            This will test the complete workflow: Group Creation → H_Assignments → Payment System Integration.
            Enter real driver and vehicle IDs to test the end-to-end system.
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="driverPartyId">Driver Party ID *</Label>
              <Input
                id="driverPartyId"
                type="number"
                placeholder="e.g. 123"
                value={formData.driverPartyId}
                onChange={(e) => handleInputChange("driverPartyId", e.target.value)}
                required
              />
              <p className="text-xs text-gray-500">
                The party ID of the driver (from party table)
              </p>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="vehicleId">Vehicle ID *</Label>
              <Input
                id="vehicleId"
                type="number"
                placeholder="e.g. 456"
                value={formData.vehicleId}
                onChange={(e) => handleInputChange("vehicleId", e.target.value)}
                required
              />
              <p className="text-xs text-gray-500">
                The ID of the vehicle (from vehicles table)
              </p>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="weeklyRate">Weekly Rate (R) *</Label>
              <Input
                id="weeklyRate"
                type="number"
                step="0.01"
                placeholder="2800.00"
                value={formData.weeklyRate}
                onChange={(e) => handleInputChange("weeklyRate", e.target.value)}
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="initiationFee">Initiation Fee (R)</Label>
              <Input
                id="initiationFee"
                type="number"
                step="0.01"
                placeholder="3000.00"
                value={formData.initiationFee}
                onChange={(e) => handleInputChange("initiationFee", e.target.value)}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              placeholder="Optional notes about this assignment..."
              value={formData.notes}
              onChange={(e) => handleInputChange("notes", e.target.value)}
              rows={3}
            />
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={loading}
              className="bg-[#009639] hover:bg-[#007A2F] text-white"
            >
              {loading ? "Creating..." : "Create Assignment"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
