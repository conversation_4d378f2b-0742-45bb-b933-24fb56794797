"use client";
import { useState, useEffect, useRef } from "react";
import { 
  Search, 
  Filter, 
  MoreVertical, 
  Send, 
  ArrowLeft,
  Clock,
  Check,
  CheckCheck,
  Car,
  User,
  MessageSquare,
  AlertCircle,
  CheckCircle,
  XCircle,
  Phone,
  Mail,
  Eye
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";

interface Driver {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  avatar?: string;
}

interface Vehicle {
  id: number;
  make: string;
  model: string;
  year: number;
  licensePlate: string;
}

interface ChatMessage {
  id: number;
  content: string;
  senderId: number;
  senderName: string;
  senderRole: "driver" | "admin";
  timestamp: Date;
  isRead: boolean;
}

interface SupportChat {
  id: number;
  type: "vehicle" | "general";
  subject: string;
  status: "active" | "resolved" | "closed";
  priority: "low" | "normal" | "high" | "urgent";
  driver: Driver;
  vehicle?: Vehicle;
  admin?: Driver;
  lastMessageAt: Date;
  unreadCount: number;
  messages: ChatMessage[];
  createdAt: Date;
}

export default function AdminSupportChat() {
  const [selectedChat, setSelectedChat] = useState<SupportChat | null>(null);
  const [message, setMessage] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<"all" | "active" | "resolved" | "closed">("all");
  const [priorityFilter, setPriorityFilter] = useState<"all" | "low" | "normal" | "high" | "urgent">("all");
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Mock data - replace with actual API calls
  const mockChats: SupportChat[] = [
    {
      id: 1,
      type: "vehicle",
      subject: "Tesla Model 3 - Charging Issue",
      status: "active",
      priority: "high",
      driver: {
        id: 1,
        firstName: "John",
        lastName: "Smith",
        email: "<EMAIL>",
        phone: "+****************"
      },
      vehicle: {
        id: 1,
        make: "Tesla",
        model: "Model 3",
        year: 2023,
        licensePlate: "ABC123"
      },
      lastMessageAt: new Date(Date.now() - 30 * 60 * 1000),
      unreadCount: 2,
      createdAt: new Date(Date.now() - 3 * 60 * 60 * 1000),
      messages: [
        {
          id: 1,
          content: "Hi, I'm having trouble with the charging port on the Tesla Model 3. It doesn't seem to be opening properly.",
          senderId: 1,
          senderName: "John Smith",
          senderRole: "driver",
          timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000),
          isRead: true,
        },
        {
          id: 2,
          content: "Thank you for reaching out. I'll help you with the charging port issue. Can you tell me if you're pressing and holding the button on the charge port?",
          senderId: 2,
          senderName: "Sarah Admin",
          senderRole: "admin",
          timestamp: new Date(Date.now() - 2.5 * 60 * 60 * 1000),
          isRead: true,
        },
        {
          id: 3,
          content: "I tried that but it's still not opening. Is there a manual way to open it?",
          senderId: 1,
          senderName: "John Smith",
          senderRole: "driver",
          timestamp: new Date(Date.now() - 30 * 60 * 1000),
          isRead: false,
        },
      ],
    },
    {
      id: 2,
      type: "general",
      subject: "Payment Method Update",
      status: "resolved",
      priority: "normal",
      driver: {
        id: 2,
        firstName: "Sarah",
        lastName: "Johnson",
        email: "<EMAIL>",
        phone: "+****************"
      },
      lastMessageAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
      unreadCount: 0,
      createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
      messages: [
        {
          id: 4,
          content: "I need help updating my payment method for bookings.",
          senderId: 2,
          senderName: "Sarah Johnson",
          senderRole: "driver",
          timestamp: new Date(Date.now() - 25 * 60 * 60 * 1000),
          isRead: true,
        },
        {
          id: 5,
          content: "Sure! You can update your payment method by going to Profile > Account > Payment Methods.",
          senderId: 2,
          senderName: "Admin",
          senderRole: "admin",
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
          isRead: true,
        },
      ],
    },
    {
      id: 3,
      type: "vehicle",
      subject: "BMW X5 - Interior Damage",
      status: "active",
      priority: "urgent",
      driver: {
        id: 3,
        firstName: "Mike",
        lastName: "Davis",
        email: "<EMAIL>",
        phone: "+****************"
      },
      vehicle: {
        id: 2,
        make: "BMW",
        model: "X5",
        year: 2022,
        licensePlate: "XYZ789"
      },
      lastMessageAt: new Date(Date.now() - 45 * 60 * 1000),
      unreadCount: 1,
      createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
      messages: [
        {
          id: 6,
          content: "I noticed a small tear in the back seat leather when I picked up the BMW X5. Should I report this immediately?",
          senderId: 3,
          senderName: "Mike Davis",
          senderRole: "driver",
          timestamp: new Date(Date.now() - 45 * 60 * 1000),
          isRead: false,
        },
      ],
    },
  ];

  const filteredChats = mockChats.filter(chat => {
    const matchesSearch = chat.subject.toLowerCase().includes(searchQuery.toLowerCase()) ||
      `${chat.driver.firstName} ${chat.driver.lastName}`.toLowerCase().includes(searchQuery.toLowerCase()) ||
      chat.driver.email.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesStatus = statusFilter === "all" || chat.status === statusFilter;
    const matchesPriority = priorityFilter === "all" || chat.priority === priorityFilter;
    
    return matchesSearch && matchesStatus && matchesPriority;
  });

  useEffect(() => {
    if (selectedChat) {
      scrollToBottom();
    }
  }, [selectedChat]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const handleSendMessage = () => {
    if (!message.trim() || !selectedChat) return;

    const newMessage: ChatMessage = {
      id: Date.now(),
      content: message,
      senderId: 999, // Admin ID
      senderName: "Admin",
      senderRole: "admin",
      timestamp: new Date(),
      isRead: false,
    };

    setSelectedChat({
      ...selectedChat,
      messages: [...selectedChat.messages, newMessage],
      lastMessageAt: new Date(),
      unreadCount: 0,
    });

    setMessage("");
  };

  const handleStatusChange = (chatId: number, newStatus: "active" | "resolved" | "closed") => {
    // Handle status change logic here
    console.log(`Changing chat ${chatId} status to ${newStatus}`);
  };

  const formatTime = (timestamp: Date) => {
    return timestamp.toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const formatDate = (timestamp: Date) => {
    const today = new Date();
    const messageDate = new Date(timestamp);
    
    if (messageDate.toDateString() === today.toDateString()) {
      return "Today";
    } else if (messageDate.toDateString() === new Date(today.getTime() - 24 * 60 * 60 * 1000).toDateString()) {
      return "Yesterday";
    } else {
      return messageDate.toLocaleDateString();
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active": return <AlertCircle size={14} className="text-orange-500" />;
      case "resolved": return <CheckCircle size={14} className="text-green-500" />;
      case "closed": return <XCircle size={14} className="text-gray-500" />;
      default: return null;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active": return "bg-orange-100 text-orange-800";
      case "resolved": return "bg-green-100 text-green-800";
      case "closed": return "bg-gray-100 text-gray-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "low": return "bg-blue-100 text-blue-800";
      case "normal": return "bg-gray-100 text-gray-800";
      case "high": return "bg-yellow-100 text-yellow-800";
      case "urgent": return "bg-red-100 text-red-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="h-screen bg-white flex">
      {/* Chat List Sidebar */}
      <div className="w-1/3 border-r border-gray-200 flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-200">
          <h1 className="text-xl font-semibold text-gray-900 mb-4">Support Chats</h1>
          
          {/* Search */}
          <div className="relative mb-3">
            <Search size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              type="text"
              placeholder="Search chats..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Filters */}
          <div className="flex gap-2">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as any)}
              className="text-sm border border-gray-300 rounded-md px-2 py-1"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="resolved">Resolved</option>
              <option value="closed">Closed</option>
            </select>
            <select
              value={priorityFilter}
              onChange={(e) => setPriorityFilter(e.target.value as any)}
              className="text-sm border border-gray-300 rounded-md px-2 py-1"
            >
              <option value="all">All Priority</option>
              <option value="low">Low</option>
              <option value="normal">Normal</option>
              <option value="high">High</option>
              <option value="urgent">Urgent</option>
            </select>
          </div>
        </div>

        {/* Chat List */}
        <div className="flex-1 overflow-y-auto">
          {filteredChats.map((chat) => (
            <div
              key={chat.id}
              onClick={() => setSelectedChat(chat)}
              className={`p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 ${
                selectedChat?.id === chat.id ? "bg-blue-50 border-blue-200" : ""
              }`}
            >
              <div className="flex items-start justify-between mb-2">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="text-sm font-medium text-gray-900 truncate">
                      {chat.driver.firstName} {chat.driver.lastName}
                    </h3>
                    {chat.unreadCount > 0 && (
                      <Badge variant="destructive" className="text-xs px-1.5 py-0.5">
                        {chat.unreadCount}
                      </Badge>
                    )}
                  </div>
                  <p className="text-xs text-gray-600 truncate mb-1">{chat.subject}</p>
                  <div className="flex items-center gap-2 mb-2">
                    <Badge className={`text-xs ${getStatusColor(chat.status)}`}>
                      {chat.status}
                    </Badge>
                    <Badge className={`text-xs ${getPriorityColor(chat.priority)}`}>
                      {chat.priority}
                    </Badge>
                  </div>
                  {chat.vehicle && (
                    <div className="flex items-center text-xs text-gray-500 mb-1">
                      <Car size={12} className="mr-1" />
                      {chat.vehicle.make} {chat.vehicle.model} • {chat.vehicle.licensePlate}
                    </div>
                  )}
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-500">
                  {formatDate(chat.lastMessageAt)} {formatTime(chat.lastMessageAt)}
                </span>
                {getStatusIcon(chat.status)}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Chat Interface */}
      <div className="flex-1 flex flex-col">
        {selectedChat ? (
          <>
            {/* Chat Header */}
            <div className="p-4 border-b border-gray-200 bg-white">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                    <User size={20} className="text-gray-500" />
                  </div>
                  <div>
                    <h2 className="font-semibold text-gray-900">
                      {selectedChat.driver.firstName} {selectedChat.driver.lastName}
                    </h2>
                    <p className="text-sm text-gray-600">{selectedChat.subject}</p>
                    {selectedChat.vehicle && (
                      <div className="flex items-center text-xs text-gray-500 mt-1">
                        <Car size={12} className="mr-1" />
                        {selectedChat.vehicle.make} {selectedChat.vehicle.model} • {selectedChat.vehicle.licensePlate}
                      </div>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge className={`${getStatusColor(selectedChat.status)}`}>
                    {selectedChat.status}
                  </Badge>
                  <Badge className={`${getPriorityColor(selectedChat.priority)}`}>
                    {selectedChat.priority}
                  </Badge>
                  <div className="flex gap-1">
                    <Button variant="outline" size="sm">
                      <Phone size={14} />
                    </Button>
                    <Button variant="outline" size="sm">
                      <Mail size={14} />
                    </Button>
                    <Button variant="outline" size="sm">
                      <Eye size={14} />
                    </Button>
                  </div>
                </div>
              </div>

              {/* Driver Info Panel */}
              <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">Email:</span>
                    <span className="ml-2 font-medium">{selectedChat.driver.email}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Phone:</span>
                    <span className="ml-2 font-medium">{selectedChat.driver.phone || "N/A"}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Created:</span>
                    <span className="ml-2 font-medium">{formatDate(selectedChat.createdAt)}</span>
                  </div>
                </div>
              </div>

              {/* Quick Actions */}
              <div className="mt-3 flex gap-2">
                <Button
                  variant={selectedChat.status === "active" ? "default" : "outline"}
                  size="sm"
                  onClick={() => handleStatusChange(selectedChat.id, "active")}
                  className="text-xs"
                >
                  Mark Active
                </Button>
                <Button
                  variant={selectedChat.status === "resolved" ? "default" : "outline"}
                  size="sm"
                  onClick={() => handleStatusChange(selectedChat.id, "resolved")}
                  className="text-xs"
                >
                  Mark Resolved
                </Button>
                <Button
                  variant={selectedChat.status === "closed" ? "default" : "outline"}
                  size="sm"
                  onClick={() => handleStatusChange(selectedChat.id, "closed")}
                  className="text-xs"
                >
                  Close Chat
                </Button>
              </div>
            </div>

            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-4 bg-[#f5f5f5]">
              <div className="space-y-4">
                {selectedChat.messages.map((msg, index) => {
                  const isFromAdmin = msg.senderRole === "admin";
                  const showDate = index === 0 || 
                    formatDate(msg.timestamp) !== formatDate(selectedChat.messages[index - 1].timestamp);

                  return (
                    <div key={msg.id}>
                      {showDate && (
                        <div className="text-center py-2">
                          <span className="bg-white px-3 py-1 rounded-full text-xs text-gray-500 shadow-sm">
                            {formatDate(msg.timestamp)}
                          </span>
                        </div>
                      )}
                      <div className={`flex ${isFromAdmin ? "justify-end" : "justify-start"}`}>
                        <div
                          className={`max-w-[70%] rounded-2xl px-4 py-2 ${
                            isFromAdmin
                              ? "bg-[#009639] text-white rounded-br-md"
                              : "bg-white text-gray-900 rounded-bl-md shadow-sm"
                          }`}
                        >
                          <div className="flex items-center justify-between mb-1">
                            <span className={`text-xs font-medium ${
                              isFromAdmin ? "text-green-100" : "text-[#009639]"
                            }`}>
                              {msg.senderName}
                            </span>
                          </div>
                          <p className="text-sm leading-relaxed">{msg.content}</p>
                          <div className={`flex items-center justify-end mt-2 space-x-1 ${
                            isFromAdmin ? "text-green-100" : "text-gray-500"
                          }`}>
                            <span className="text-xs">{formatTime(msg.timestamp)}</span>
                            {isFromAdmin && (
                              msg.isRead ? (
                                <CheckCheck size={12} className="text-blue-200" />
                              ) : (
                                <Check size={12} className="text-green-200" />
                              )
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
                <div ref={messagesEndRef} />
              </div>
            </div>

            {/* Message Input */}
            <div className="p-4 bg-white border-t border-gray-200">
              <div className="flex items-center gap-3">
                <Input
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  placeholder="Type your message..."
                  className="flex-1"
                  onKeyPress={(e) => e.key === "Enter" && handleSendMessage()}
                />
                <Button
                  onClick={handleSendMessage}
                  disabled={!message.trim()}
                  className="bg-[#009639] hover:bg-[#007A2F] text-white"
                >
                  <Send size={18} />
                </Button>
              </div>
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center bg-gray-50">
            <div className="text-center">
              <MessageSquare size={48} className="text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Select a chat</h3>
              <p className="text-gray-600">Choose a support chat from the list to start helping drivers</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}