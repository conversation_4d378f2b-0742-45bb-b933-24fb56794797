"use client";

import React, { useState } from "react";
import {
  Search,
  Filter,
  Download,
  Clock,
  User,
  Activity,
  Settings,
  RefreshCw,
} from "lucide-react";

interface AuditEvent {
  id: string;
  timestamp: string;
  user: string;
  action: string;
  resource: string;
  details: string;
  ipAddress: string;
}

const mockAuditEvents: AuditEvent[] = [
  {
    id: "1",
    timestamp: "2024-01-20 10:30:15",
    user: "<EMAIL>",
    action: "User Update",
    resource: "User Settings",
    details: "Updated user role from Member to Admin",
    ipAddress: "*************",
  },
  {
    id: "2",
    timestamp: "2024-01-20 10:29:45",
    user: "system",
    action: "Configuration Change",
    resource: "System Settings",
    details: "Updated email notification settings",
    ipAddress: "localhost",
  },
  {
    id: "3",
    timestamp: "2024-01-20 10:28:30",
    user: "<EMAIL>",
    action: "Group Creation",
    resource: "Groups",
    details: "Created new group: Marketing Team",
    ipAddress: "*************",
  },
  {
    id: "4",
    timestamp: "2024-01-20 10:27:15",
    user: "<EMAIL>",
    action: "Login",
    resource: "Authentication",
    details: "Successful login attempt",
    ipAddress: "*************",
  },
];

export default function AuditTrails() {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedAction, setSelectedAction] = useState<string>("all");

  const getActionIcon = (action: string) => {
    switch (action.toLowerCase()) {
      case "login":
        return <User size={18} className="text-blue-500" />;
      case "configuration change":
        return <Settings size={18} className="text-purple-500" />;
      case "user update":
        return <Activity size={18} className="text-orange-500" />;
      default:
        return <Clock size={18} className="text-gray-500" />;
    }
  };

  const filteredEvents = mockAuditEvents.filter((event) => {
    const matchesSearch =
      event.user.toLowerCase().includes(searchQuery.toLowerCase()) ||
      event.action.toLowerCase().includes(searchQuery.toLowerCase()) ||
      event.details.toLowerCase().includes(searchQuery.toLowerCase()) ||
      event.resource.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesAction =
      selectedAction === "all" ||
      event.action.toLowerCase() === selectedAction.toLowerCase();

    return matchesSearch && matchesAction;
  });

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800">Audit Trails</h1>
        <p className="text-gray-500">Track and monitor system activities</p>
      </div>

      <div className="bg-white rounded-xl shadow-sm p-6">
        {/* Controls */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
          <div className="flex-1 w-full md:w-auto">
            <div className="relative">
              <Search
                size={18}
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
              />
              <input
                type="text"
                placeholder="Search audit trails..."
                className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>

          <div className="flex items-center space-x-4 w-full md:w-auto">
            <div className="relative flex-1 md:flex-none">
              <select
                className="w-full md:w-auto appearance-none pl-4 pr-10 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent"
                value={selectedAction}
                onChange={(e) => setSelectedAction(e.target.value)}
              >
                <option value="all">All Actions</option>
                <option value="login">Login</option>
                <option value="user update">User Update</option>
                <option value="configuration change">
                  Configuration Change
                </option>
                <option value="group creation">Group Creation</option>
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                <Filter size={16} className="text-gray-400" />
              </div>
            </div>

            <button className="flex items-center space-x-2 px-4 py-2 border border-gray-200 rounded-md text-sm text-gray-600 hover:bg-gray-50">
              <Download size={16} />
              <span>Export</span>
            </button>

            <button className="flex items-center space-x-2 px-4 py-2 border border-gray-200 rounded-md text-sm text-gray-600 hover:bg-gray-50">
              <RefreshCw size={16} />
              <span>Refresh</span>
            </button>
          </div>
        </div>

        {/* Audit Events Table */}
        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                  Timestamp
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                  User
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                  Action
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                  Resource
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                  Details
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                  IP Address
                </th>
              </tr>
            </thead>
            <tbody>
              {filteredEvents.map((event) => (
                <tr
                  key={event.id}
                  className="border-b border-gray-200 hover:bg-gray-50"
                >
                  <td className="px-4 py-4 text-sm text-gray-500">
                    {event.timestamp}
                  </td>
                  <td className="px-4 py-4 text-sm text-gray-900">
                    {event.user}
                  </td>
                  <td className="px-4 py-4">
                    <div className="flex items-center space-x-2">
                      {getActionIcon(event.action)}
                      <span className="text-sm text-gray-900">
                        {event.action}
                      </span>
                    </div>
                  </td>
                  <td className="px-4 py-4 text-sm text-gray-500">
                    {event.resource}
                  </td>
                  <td className="px-4 py-4 text-sm text-gray-500">
                    {event.details}
                  </td>
                  <td className="px-4 py-4 text-sm text-gray-500">
                    {event.ipAddress}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredEvents.length === 0 && (
          <div className="text-center py-8">
            <p className="text-gray-500">
              No audit events found matching your criteria
            </p>
          </div>
        )}

        {/* Pagination */}
        <div className="flex items-center justify-between mt-6">
          <div className="text-sm text-gray-500">
            Showing {filteredEvents.length} entries
          </div>

          <div className="flex items-center space-x-1">
            <button className="px-3 py-1 border border-gray-200 rounded-md text-sm text-gray-500 hover:bg-gray-50">
              &lt;
            </button>
            <button className="px-3 py-1 border border-gray-200 rounded-md text-sm bg-[#009639] text-white">
              1
            </button>
            <button className="px-3 py-1 border border-gray-200 rounded-md text-sm text-gray-500 hover:bg-gray-50">
              &gt;
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
