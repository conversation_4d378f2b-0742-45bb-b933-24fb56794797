"use client";

import { useState, useEffect, useRef } from "react";
import {
  Search,
  Filter,
  MessageSquare,
  User,
  FileText,
  Car,
  Users,
  Clock,
  CheckCircle,
  AlertCircle,
  Phone,
  Mail,
  MapPin,
  Calendar,
  DollarSign,
  Settings,
  Eye,
  Download,
  ChevronRight,
  Send,
  Paperclip,
  Loader2,
} from "lucide-react";

import {
  useAdminSupportChats,
  useAdminSupportChatMessages,
  useAdminSupportChatActions,
  useCustomerDetails,
  useAdminChatFilters,
  useAdminChatSelection
} from "@/hooks/useAdminSupportChat";

interface CustomerDetails {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  joinDate: string;
  status: "active" | "inactive" | "suspended";
  kycStatus: "verified" | "pending" | "rejected";
  profileImage?: string;
}

interface SupportChat {
  id: number;
  customer: CustomerDetails;
  subject: string;
  status: "active" | "resolved" | "closed";
  priority: "low" | "normal" | "high" | "urgent";
  type: "general" | "vehicle" | "financial" | "safety";
  lastMessage: string;
  lastMessageTime: string;
  unreadCount: number;
  assignedTo?: string;
}

interface CustomerApplication {
  id: number;
  type: string;
  status: string;
  submittedDate: string;
  vehicleMake?: string;
  vehicleModel?: string;
}

interface CustomerVehicle {
  id: number;
  make: string;
  model: string;
  year: number;
  licensePlate: string;
  status: "active" | "maintenance" | "inactive";
}

interface CustomerGroup {
  id: number;
  name: string;
  role: "owner" | "member";
  memberCount: number;
  status: "active" | "inactive";
}

interface CustomerDocument {
  id: number;
  name: string;
  type: string;
  status: "verified" | "pending" | "rejected";
  uploadDate: string;
}

export default function CustomerSupportPage() {
  const [newMessage, setNewMessage] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Use custom hooks for state management
  const { filters, updateFilter, clearFilters } = useAdminChatFilters();
  const { selectedChat, customerDetailsTab, selectChat, setCustomerDetailsTab } = useAdminChatSelection();
  
  // Fetch data using hooks
  const { chats, isLoading: chatsLoading, mutate: mutateChats, totalUnread } = useAdminSupportChats(filters);
  const { messages, isLoading: messagesLoading, mutate: mutateMessages } = useAdminSupportChatMessages(
    selectedChat?.id || null
  );
  const { sendMessage, assignChat, updateStatus, markAsRead } = useAdminSupportChatActions();
  const { customer, isLoading: customerLoading } = useCustomerDetails(
    selectedChat?.driverPartyId || null
  );

  // Mark messages as read when chat is selected
  useEffect(() => {
    if (selectedChat) {
      markAsRead(selectedChat.id);
    }
  }, [selectedChat, markAsRead]);

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Handle sending messages
  const handleSendMessage = async () => {
    if (!newMessage.trim() || !selectedChat) return;
    
    const messageContent = newMessage.trim();
    setNewMessage("");
    setIsTyping(true);
    
    try {
      const result = await sendMessage({
        chatId: selectedChat.id,
        content: messageContent,
        messageType: "text"
      });
      
      if (result.success) {
        mutateMessages(); // Refresh messages
        mutateChats(); // Refresh chat list to update last message
      } else {
        console.error("Failed to send message:", result.error);
      }
    } catch (error) {
      console.error("Error sending message:", error);
    } finally {
      setIsTyping(false);
    }
  };

  // Handle chat status updates
  const handleStatusUpdate = async (status: "active" | "resolved" | "closed") => {
    if (!selectedChat) return;
    
    try {
      const result = await updateStatus(selectedChat.id, status);
      if (result.success) {
        mutateChats(); // Refresh chat list
      }
    } catch (error) {
      console.error("Error updating status:", error);
    }
  };

  // Mock data - replace with real data fetching
  const mockChats: SupportChat[] = [
    {
      id: 1,
      customer: {
        id: 1,
        firstName: "John",
        lastName: "Mbeki",
        email: "<EMAIL>",
        phone: "+27 82 123 4567",
        address: "123 Main Street, Johannesburg, 2001",
        joinDate: "2024-01-15",
        status: "active",
        kycStatus: "verified",
      },
      subject: "Vehicle booking issue",
      status: "active",
      priority: "high",
      type: "vehicle",
      lastMessage: "I'm unable to book the Toyota Hilux for this weekend",
      lastMessageTime: "2 minutes ago",
      unreadCount: 3,
      assignedTo: "Sarah Admin",
    },
    {
      id: 2,
      customer: {
        id: 2,
        firstName: "Thabo",
        lastName: "Nkomo",
        email: "<EMAIL>",
        phone: "+27 83 987 6543",
        address: "456 Oak Avenue, Cape Town, 8001",
        joinDate: "2024-02-20",
        status: "active",
        kycStatus: "pending",
      },
      subject: "Payment verification",
      status: "active",
      priority: "normal",
      type: "financial",
      lastMessage: "My payment was declined, can you help?",
      lastMessageTime: "1 hour ago",
      unreadCount: 1,
    },
    {
      id: 3,
      customer: {
        id: 3,
        firstName: "Nomsa",
        lastName: "Dlamini",
        email: "<EMAIL>",
        phone: "+27 84 555 7890",
        address: "789 Elm Street, Durban, 4001",
        joinDate: "2023-11-10",
        status: "active",
        kycStatus: "verified",
      },
      subject: "General support inquiry",
      status: "resolved",
      priority: "low",
      type: "general",
      lastMessage: "Thank you for your help!",
      lastMessageTime: "Yesterday",
      unreadCount: 0,
    },
  ];

  const mockApplications: CustomerApplication[] = [
    {
      id: 1,
      type: "Vehicle Lease Application",
      status: "approved",
      submittedDate: "2024-01-20",
      vehicleMake: "Toyota",
      vehicleModel: "Hilux",
    },
    {
      id: 2,
      type: "Co-ownership Application",
      status: "under_review",
      submittedDate: "2024-01-25",
      vehicleMake: "Ford",
      vehicleModel: "Ranger",
    },
  ];

  const mockVehicles: CustomerVehicle[] = [
    {
      id: 1,
      make: "Toyota",
      model: "Hilux",
      year: 2022,
      licensePlate: "GP 123 ABC",
      status: "active",
    },
  ];

  const mockGroups: CustomerGroup[] = [
    {
      id: 1,
      name: "Johannesburg Fleet Group",
      role: "member",
      memberCount: 8,
      status: "active",
    },
  ];

  const mockDocuments: CustomerDocument[] = [
    {
      id: 1,
      name: "Driver's License",
      type: "identification",
      status: "verified",
      uploadDate: "2024-01-15",
    },
    {
      id: 2,
      name: "Proof of Address",
      type: "address_verification",
      status: "verified",
      uploadDate: "2024-01-15",
    },
    {
      id: 3,
      name: "Bank Statement",
      type: "financial",
      status: "pending",
      uploadDate: "2024-01-25",
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "text-green-600 bg-green-100";
      case "resolved":
        return "text-blue-600 bg-blue-100";
      case "closed":
        return "text-gray-600 bg-gray-100";
      case "pending":
        return "text-yellow-600 bg-yellow-100";
      case "rejected":
        return "text-red-600 bg-red-100";
      case "verified":
        return "text-green-600 bg-green-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "urgent":
        return "text-red-600";
      case "high":
        return "text-orange-600";
      case "normal":
        return "text-blue-600";
      case "low":
        return "text-gray-600";
      default:
        return "text-gray-600";
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "vehicle":
        return <Car size={16} />;
      case "financial":
        return <DollarSign size={16} />;
      case "safety":
        return <AlertCircle size={16} />;
      default:
        return <MessageSquare size={16} />;
    }
  };

  return (
    <div className="h-screen flex bg-gray-50">
      {/* Column 1: Chat List */}
      <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-200">
          <h1 className="text-xl font-semibold text-gray-900 mb-4">Customer Support</h1>
          
          {/* Search */}
          <div className="relative mb-3">
            <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search chats..."
              value={filters.search || ""}
              onChange={(e) => updateFilter("search", e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#009639] focus:border-transparent"
            />
          </div>

          {/* Filters */}
          <div className="flex space-x-2">
            <select
              value={filters.status || "all"}
              onChange={(e) => updateFilter("status", e.target.value === "all" ? undefined : e.target.value)}
              className="flex-1 px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-[#009639]"
            >
              <option value="all">All Chats</option>
              <option value="active">Active</option>
              <option value="resolved">Resolved</option>
              <option value="closed">Closed</option>
            </select>
            <button className="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50">
              <Filter size={16} />
            </button>
          </div>
        </div>

        {/* Chat List */}
        <div className="flex-1 overflow-y-auto">
          {chatsLoading ? (
            <div className="p-4 text-center">
              <Loader2 size={20} className="animate-spin mx-auto mb-2" />
              <p className="text-sm text-gray-600">Loading chats...</p>
            </div>
          ) : chats.length > 0 ? (
            chats.map((chat) => (
            <div
              key={chat.id}
              onClick={() => selectChat(chat)}
              className={`p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 ${
                selectedChat?.id === chat.id ? "bg-blue-50 border-l-4 border-l-[#009639]" : ""
              }`}
            >
              <div className="flex items-start justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-[#009639] rounded-full flex items-center justify-center text-white text-sm font-medium">
                    {chat.driver?.firstName?.[0] || 'U'}{chat.driver?.lastName?.[0] || 'U'}
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">
                      {chat.driver?.firstName || 'Unknown'} {chat.driver?.lastName || 'User'}
                    </h3>
                    <div className="flex items-center space-x-2">
                      {getTypeIcon(chat.type)}
                      <span className="text-sm text-gray-600">{chat.subject}</span>
                    </div>
                  </div>
                </div>
                {chat.unreadCount && chat.unreadCount > 0 && (
                  <span className="bg-[#009639] text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                    {chat.unreadCount}
                  </span>
                )}
              </div>
              
              <p className="text-sm text-gray-600 truncate mb-2">
                {chat.lastMessage?.content || 'No messages yet'}
              </p>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(chat.status)}`}>
                    {chat.status}
                  </span>
                  <span className={`text-xs font-medium ${getPriorityColor(chat.priority || 'normal')}`}>
                    {chat.priority || 'normal'}
                  </span>
                </div>
                <span className="text-xs text-gray-500">
                  {chat.lastMessage?.createdAt 
                    ? new Date(chat.lastMessage.createdAt).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
                    : new Date(chat.createdAt).toLocaleDateString()
                  }
                </span>
              </div>
            </div>
          ))
          ) : (
            <div className="p-4 text-center">
              <MessageSquare size={32} className="mx-auto text-gray-400 mb-2" />
              <p className="text-sm text-gray-600">No chats found</p>
              {filters.search && (
                <button 
                  onClick={clearFilters}
                  className="text-xs text-[#009639] hover:underline mt-1"
                >
                  Clear filters
                </button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Column 2: Chat Conversation */}
      <div className="flex-1 flex flex-col bg-white border-r border-gray-200">
        {selectedChat ? (
          <>
            {/* Chat Header */}
            <div className="p-4 border-b border-gray-200 bg-gray-50">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-[#009639] rounded-full flex items-center justify-center text-white font-medium">
                    {selectedChat.driver?.firstName?.[0] || 'U'}{selectedChat.driver?.lastName?.[0] || 'U'}
                  </div>
                  <div>
                    <h2 className="font-semibold text-gray-900">
                      {selectedChat.driver?.firstName || 'Unknown'} {selectedChat.driver?.lastName || 'User'}
                    </h2>
                    <p className="text-sm text-gray-600">{selectedChat.subject}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(selectedChat.status)}`}>
                    {selectedChat.status}
                  </span>
                  <button className="p-2 hover:bg-gray-200 rounded">
                    <Settings size={16} />
                  </button>
                </div>
              </div>
            </div>

            {/* Messages Area */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {messagesLoading ? (
                <div className="flex items-center justify-center h-32">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#009639]"></div>
                </div>
              ) : messages.length > 0 ? (
                messages.map((message) => {
                  const isFromAdmin = message.sender?.role === 'admin';
                  const messageTime = new Date(message.createdAt).toLocaleTimeString([], { 
                    hour: '2-digit', 
                    minute: '2-digit' 
                  });
                  
                  return (
                    <div key={message.id} className={`flex ${isFromAdmin ? 'justify-end' : 'justify-start'}`}>
                      <div className={`rounded-lg px-4 py-2 max-w-xs ${
                        isFromAdmin 
                          ? 'bg-[#009639] text-white' 
                          : 'bg-gray-100 text-gray-900'
                      }`}>
                        <p className="text-sm">{message.content}</p>
                        <p className={`text-xs mt-1 ${
                          isFromAdmin ? 'text-green-100' : 'text-gray-500'
                        }`}>
                          {messageTime}
                        </p>
                      </div>
                    </div>
                  );
                })
              ) : (
                <div className="flex items-center justify-center h-32">
                  <div className="text-center text-gray-500">
                    <MessageSquare size={32} className="mx-auto mb-2" />
                    <p className="text-sm">No messages yet</p>
                  </div>
                </div>
              )}
              <div ref={messagesEndRef} />
            </div>

            {/* Message Input */}
            <div className="p-4 border-t border-gray-200">
              <div className="flex items-center space-x-2">
                <button className="p-2 hover:bg-gray-100 rounded">
                  <Paperclip size={18} />
                </button>
                <div className="flex-1 border border-gray-300 rounded-lg">
                  <input
                    type="text"
                    placeholder="Type a message..."
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && !isTyping && handleSendMessage()}
                    disabled={isTyping}
                    className="w-full px-3 py-2 border-0 rounded-lg focus:ring-0"
                  />
                </div>
                <button 
                  onClick={handleSendMessage}
                  disabled={isTyping || !newMessage.trim()}
                  className="p-2 bg-[#009639] text-white rounded-lg hover:bg-[#007A2F] disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isTyping ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  ) : (
                    <Send size={18} />
                  )}
                </button>
              </div>
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <MessageSquare size={48} className="mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Select a conversation</h3>
              <p className="text-gray-600">Choose a chat from the left to start messaging</p>
            </div>
          </div>
        )}
      </div>

      {/* Column 3: Customer Details */}
      <div className="w-96 bg-white flex flex-col">
        {selectedChat ? (
          <>
            {/* Customer Header */}
            <div className="p-4 border-b border-gray-200 bg-gray-50">
              <div className="text-center">
                {customerLoading ? (
                  <div className="py-8">
                    <Loader2 size={24} className="animate-spin mx-auto mb-2" />
                    <p className="text-sm text-gray-600">Loading customer details...</p>
                  </div>
                ) : customer ? (
                  <>
                    <div className="w-16 h-16 bg-[#009639] rounded-full flex items-center justify-center text-white text-xl font-bold mx-auto mb-3">
                      {customer.firstName?.[0] || 'U'}{customer.lastName?.[0] || 'U'}
                    </div>
                    <h3 className="font-semibold text-gray-900">
                      {customer.firstName || 'Unknown'} {customer.lastName || 'User'}
                    </h3>
                    <p className="text-sm text-gray-600">{customer.email || 'No email'}</p>
                    <div className="flex justify-center space-x-2 mt-3">
                      <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(customer.status || 'active')}`}>
                        {customer.status || 'active'}
                      </span>
                      <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(customer.kycStatus || 'pending')}`}>
                        KYC: {customer.kycStatus || 'pending'}
                      </span>
                    </div>
                  </>
                ) : (
                  <div className="py-8">
                    <User size={32} className="mx-auto text-gray-400 mb-2" />
                    <p className="text-sm text-gray-600">Customer details unavailable</p>
                  </div>
                )}
              </div>
            </div>

            {/* Tab Navigation */}
            <div className="border-b border-gray-200">
              <nav className="flex">
                {[
                  { id: "profile", label: "Profile", icon: User },
                  { id: "applications", label: "Applications", icon: FileText },
                  { id: "vehicles", label: "Vehicles", icon: Car },
                  { id: "groups", label: "Groups", icon: Users },
                  { id: "documents", label: "Documents", icon: FileText },
                ].map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setCustomerDetailsTab(tab.id)}
                    className={`flex-1 flex items-center justify-center px-3 py-2 text-xs font-medium border-b-2 ${
                      customerDetailsTab === tab.id
                        ? "border-[#009639] text-[#009639]"
                        : "border-transparent text-gray-500 hover:text-gray-700"
                    }`}
                  >
                    <tab.icon size={14} className="mr-1" />
                    {tab.label}
                  </button>
                ))}
              </nav>
            </div>

            {/* Tab Content */}
            <div className="flex-1 overflow-y-auto p-4">
              {customerDetailsTab === "profile" && (
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Contact Information</h4>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Phone size={16} className="text-gray-400" />
                        <span className="text-sm">{customer?.phone || 'Not provided'}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Mail size={16} className="text-gray-400" />
                        <span className="text-sm">{customer?.email || 'Not provided'}</span>
                      </div>
                      <div className="flex items-start space-x-2">
                        <MapPin size={16} className="text-gray-400 mt-0.5" />
                        <span className="text-sm">{customer?.address || 'Not provided'}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Account Details</h4>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Calendar size={16} className="text-gray-400" />
                        <span className="text-sm">Joined {customer?.joinDate ? new Date(customer.joinDate).toLocaleDateString() : 'Unknown'}</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {customerDetailsTab === "applications" && (
                <div className="space-y-3">
                  {mockApplications.map((app) => (
                    <div key={app.id} className="p-3 border border-gray-200 rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium text-sm">{app.type}</h4>
                        <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(app.status)}`}>
                          {app.status}
                        </span>
                      </div>
                      {app.vehicleMake && (
                        <p className="text-sm text-gray-600">{app.vehicleMake} {app.vehicleModel}</p>
                      )}
                      <p className="text-xs text-gray-500 mt-1">Submitted: {app.submittedDate}</p>
                      <button className="text-xs text-[#009639] hover:underline mt-2">View Details</button>
                    </div>
                  ))}
                </div>
              )}

              {customerDetailsTab === "vehicles" && (
                <div className="space-y-3">
                  {mockVehicles.map((vehicle) => (
                    <div key={vehicle.id} className="p-3 border border-gray-200 rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium text-sm">{vehicle.make} {vehicle.model}</h4>
                        <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(vehicle.status)}`}>
                          {vehicle.status}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600">{vehicle.year} • {vehicle.licensePlate}</p>
                      <button className="text-xs text-[#009639] hover:underline mt-2">View Details</button>
                    </div>
                  ))}
                </div>
              )}

              {customerDetailsTab === "groups" && (
                <div className="space-y-3">
                  {mockGroups.map((group) => (
                    <div key={group.id} className="p-3 border border-gray-200 rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium text-sm">{group.name}</h4>
                        <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(group.status)}`}>
                          {group.status}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600">Role: {group.role} • {group.memberCount} members</p>
                      <button className="text-xs text-[#009639] hover:underline mt-2">View Group</button>
                    </div>
                  ))}
                </div>
              )}

              {customerDetailsTab === "documents" && (
                <div className="space-y-3">
                  {mockDocuments.map((doc) => (
                    <div key={doc.id} className="p-3 border border-gray-200 rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium text-sm">{doc.name}</h4>
                        <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(doc.status)}`}>
                          {doc.status}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 capitalize">{doc.type.replace('_', ' ')}</p>
                      <p className="text-xs text-gray-500 mt-1">Uploaded: {doc.uploadDate}</p>
                      <div className="flex space-x-2 mt-2">
                        <button className="text-xs text-[#009639] hover:underline flex items-center">
                          <Eye size={12} className="mr-1" />
                          View
                        </button>
                        <button className="text-xs text-[#009639] hover:underline flex items-center">
                          <Download size={12} className="mr-1" />
                          Download
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <User size={48} className="mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Customer Details</h3>
              <p className="text-gray-600">Select a chat to view customer information</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}