"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  ArrowLeft,
  Users,
  MapPin,
  FileText,
  Building,
  Mail,
  Plus,
  X,
  Car,
  Hash,
  Calendar,
  Palette,
} from "lucide-react";
import { createGroup } from "../../../drizzle-actions/groups";
import { getCities, getCountriesWithIds, getVehicleModels } from "../../../drizzle-actions/community";
import { CompanyPurposeEnum } from "../../../types/company";
import { GroupRoleEnum } from "../../../types/groups";
import { useCurrentUser } from "../../../hooks/use-current-user";
import { Switch } from "@/components/ui/switch";

interface City {
  id: number;
  name: string;
  province: string;
  country: string;
}

interface Country {
  id: number;
  name: string;
  code: string;
}

interface MemberInvitation {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: GroupRoleEnum;
}

interface VehicleModel {
  id: number;
  model: string;
  year_model: number;
  make_name: string;
}

export default function CreateGroupScreen() {
  const router = useRouter();
  const { partyId: currentUserPartyId, isLoading: userLoading } = useCurrentUser();
  
  // Group Information
  const [groupName, setGroupName] = useState("");
  const [description, setDescription] = useState("");
  const [purpose, setPurpose] = useState<CompanyPurposeEnum>(CompanyPurposeEnum.OTHER);
  const [isManaged, setIsManaged] = useState(false);
  const [selectedCityId, setSelectedCityId] = useState<number | undefined>();
  const [selectedCountryId, setSelectedCountryId] = useState<number>(1); // Default to South Africa
  
  // Company Information
  const [createCompany, setCreateCompany] = useState(false);
  const [companyName, setCompanyName] = useState("");
  const [registrationNumber, setRegistrationNumber] = useState("");
  const [registrationCountry, setRegistrationCountry] = useState("South Africa");
  
  // Vehicle Information (Required)
  const [vinNumber, setVinNumber] = useState("");
  const [vehicleRegistration, setVehicleRegistration] = useState("");
  const [manufacturingYear, setManufacturingYear] = useState<number>(new Date().getFullYear());
  const [color, setColor] = useState("");
  const [selectedModelId, setSelectedModelId] = useState<number | undefined>();
  
  // Member Invitations
  const [memberInvitations, setMemberInvitations] = useState<MemberInvitation[]>([]);
  const [newMemberEmail, setNewMemberEmail] = useState("");
  const [newMemberFirstName, setNewMemberFirstName] = useState("");
  const [newMemberLastName, setNewMemberLastName] = useState("");
  const [newMemberRole, setNewMemberRole] = useState<GroupRoleEnum>(GroupRoleEnum.MEMBER);
  
  // Data and State
  const [cities, setCities] = useState<City[]>([]);
  const [countries, setCountries] = useState<Country[]>([]);
  const [vehicleModels, setVehicleModels] = useState<VehicleModel[]>([]);
  const [loading, setLoading] = useState(false);
  const [creating, setCreating] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        
        // Fetch all data in parallel
        const [cityList, countryList, vehicleModelList] = await Promise.all([
          getCities(),
          getCountriesWithIds(),
          getVehicleModels(),
        ]);
        
        setCities(cityList);
        setCountries(countryList);
        setVehicleModels(vehicleModelList);
        
      } catch (error) {
        console.error("Failed to fetch data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const addMemberInvitation = () => {
    if (
      !newMemberEmail.trim() ||
      !newMemberFirstName.trim() ||
      !newMemberLastName.trim()
    ) {
      alert("Please fill in all member details");
      return;
    }

    if (memberInvitations.some((m) => m.email === newMemberEmail)) {
      alert("This email is already in the invitation list");
      return;
    }

    const newInvitation: MemberInvitation = {
      id: Date.now().toString(),
      email: newMemberEmail.trim(),
      firstName: newMemberFirstName.trim(),
      lastName: newMemberLastName.trim(),
      role: newMemberRole,
    };

    setMemberInvitations([...memberInvitations, newInvitation]);
    setNewMemberEmail("");
    setNewMemberFirstName("");
    setNewMemberLastName("");
    setNewMemberRole(GroupRoleEnum.MEMBER);
  };

  const removeMemberInvitation = (id: string) => {
    setMemberInvitations(memberInvitations.filter((m) => m.id !== id));
  };

  const handleCreateGroup = async () => {
    if (!currentUserPartyId) {
      alert("User not authenticated");
      return;
    }

    if (!groupName.trim()) {
      alert("Please enter a group name");
      return;
    }

    if (!description.trim()) {
      alert("Please enter a description");
      return;
    }

    if (!selectedCityId) {
      alert("Please select a city");
      return;
    }

    if (!vinNumber.trim()) {
      alert("Please enter a VIN number");
      return;
    }

    if (!selectedModelId) {
      alert("Please select a vehicle model");
      return;
    }

    try {
      setCreating(true);

      const groupData = {
        groupCreate: {
          name: groupName.trim(),
          description: description.trim(),
          cityId: selectedCityId,
          countryId: selectedCountryId,
          InitialPurpose: purpose,
          isManaged: isManaged,
          createdBy: currentUserPartyId,
        },
        memberInvitations: memberInvitations.length > 0 ? memberInvitations.map(invite => ({
          firstName: invite.firstName,
          lastName: invite.lastName,
          email: invite.email,
          role: invite.role,
        })) : undefined,
        vehicleCreate: {
          model_id: selectedModelId,
          vin_number: vinNumber.trim(),
          vehicle_registration: vehicleRegistration.trim() || undefined,
          country_id: selectedCountryId,
          manufacturing_year: manufacturingYear,
          color: color.trim() || undefined,
          is_active: true,
        },
      };

      const result = await createGroup(groupData);

      if (result && result.group) {
        alert("Group created successfully!");
        router.push(`/group-details/${result.group.id}`);
      } else {
        alert("Failed to create group");
      }
    } catch (error) {
      console.error("Error creating group:", error);
      alert("Failed to create group. Please try again.");
    } finally {
      setCreating(false);
    }
  };

  const purposeOptions = [
    { value: CompanyPurposeEnum.RIDE_SHARE, label: "Ride Share", description: "Share vehicles for transportation services" },
    { value: CompanyPurposeEnum.FLEET, label: "Fleet Management", description: "Manage a fleet of vehicles" },
    { value: CompanyPurposeEnum.GROUP_MONETIZATION, label: "Group Monetization", description: "Generate income through group activities" },
    { value: CompanyPurposeEnum.OTHER, label: "Other", description: "Other purposes not listed above" },
    { value: CompanyPurposeEnum.EHAILING_DRIVER, label: "eHailing Driver", description: "Driver for Poolly eHailing services" },
  ];

  return (
    <div className="min-h-screen">
      {/* Header */}
      <div className="bg-[#009639] px-6 py-4 flex items-center border-b border-[#007A2F]">
        <button className="mr-4" onClick={() => router.back()}>
          <ArrowLeft size={24} className="text-white" />
        </button>
        <h1 className="text-xl font-bold text-white">Create Group</h1>
      </div>

      {/* Form */}
      <div className="p-6 pb-24">
        {/* Basic Information */}
        <div className="bg-white rounded-xl shadow-md border border-gray-100 p-6 mb-6">
          <h2 className="text-lg font-semibold text-[#333333] mb-6">
            Group Information
          </h2>

          {/* Group Name */}
          <div className="mb-4">
            <label className="flex items-center text-sm font-medium text-[#333333] mb-2">
              <Users size={16} className="mr-2 text-[#009639]" />
              Group Name
            </label>
            <input
              type="text"
              value={groupName}
              onChange={(e) => setGroupName(e.target.value)}
              placeholder="Enter group name (e.g., City Commuters)"
              className="w-full px-4 py-3 border border-[#e5e5e5] rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
              maxLength={100}
            />
          </div>

          {/* Description */}
          <div className="mb-4">
            <label className="flex items-center text-sm font-medium text-[#333333] mb-2">
              <FileText size={16} className="mr-2 text-[#009639]" />
              Description
            </label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Describe your group's purpose"
              className="w-full px-4 py-3 border border-[#e5e5e5] rounded-lg focus:outline-none focus:border-[#009639] text-[#333333] resize-none"
              rows={3}
              maxLength={500}
            />
            <p className="text-xs text-[#797879] mt-1">
              {description.length}/500 characters
            </p>
          </div>

          {/* Purpose */}
          <div className="mb-4">
            <label className="flex items-center text-sm font-medium text-[#333333] mb-2">
              <Building size={16} className="mr-2 text-[#009639]" />
              Purpose
            </label>
            <select
              value={purpose}
              onChange={(e) => setPurpose(e.target.value as CompanyPurposeEnum)}
              className="w-full px-4 py-3 border border-[#e5e5e5] rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
            >
              {purposeOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* Location */}
          <div className="mb-4">
            <label className="flex items-center text-sm font-medium text-[#333333] mb-2">
              <MapPin size={16} className="mr-2 text-[#009639]" />
              Location
            </label>
            {loading ? (
              <div className="w-full px-4 py-3 border border-[#e5e5e5] rounded-lg bg-gray-100 animate-pulse">
                Loading cities...
              </div>
            ) : (
              <select
                value={selectedCityId || ""}
                onChange={(e) =>
                  setSelectedCityId(
                    e.target.value ? parseInt(e.target.value) : undefined
                  )
                }
                className="w-full px-4 py-3 border border-[#e5e5e5] rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
                required
              >
                <option value="">Select a city</option>
                {cities.map((city) => (
                  <option key={city.id} value={city.id}>
                    {city.name}, {city.province}, {city.country}
                  </option>
                ))}
              </select>
            )}
          </div>

          {/* Managed Group */}
          <div className="mb-4">
            <label className="flex items-center text-sm font-medium text-[#333333] mb-2">
              <Building size={16} className="mr-2 text-[#009639]" />
              <span className="mr-2">Managed Group</span>
              <Switch checked={isManaged} onCheckedChange={setIsManaged} />
            </label>
            <p className="text-xs text-[#797879] ml-6">
              Check if this group will be professionally managed
            </p>
          </div>
        </div>

        {/* Vehicle Information */}
        <div className="bg-white rounded-xl shadow-md border border-gray-100 p-6 mb-6">
          <h2 className="text-lg font-semibold text-[#333333] mb-6">
            Vehicle Information
          </h2>

          {/* Vehicle Model */}
          <div className="mb-4">
            <label className="flex items-center text-sm font-medium text-[#333333] mb-2">
              <Car size={16} className="mr-2 text-[#009639]" />
              Vehicle Model
            </label>
            <select
              value={selectedModelId || ""}
              onChange={(e) =>
                setSelectedModelId(
                  e.target.value ? parseInt(e.target.value) : undefined
                )
              }
              className="w-full px-4 py-3 border border-[#e5e5e5] rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
              required
            >
              <option value="">Select a vehicle model</option>
              {vehicleModels.map((model) => (
                <option key={model.id} value={model.id}>
                  {model.make_name} {model.model} ({model.year_model})
                </option>
              ))}
            </select>
          </div>

          {/* VIN Number */}
          <div className="mb-4">
            <label className="flex items-center text-sm font-medium text-[#333333] mb-2">
              <Hash size={16} className="mr-2 text-[#009639]" />
              VIN Number
            </label>
            <input
              type="text"
              value={vinNumber}
              onChange={(e) => setVinNumber(e.target.value)}
              placeholder="Enter VIN number"
              className="w-full px-4 py-3 border border-[#e5e5e5] rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
              maxLength={17}
              required
            />
          </div>

          {/* Vehicle Registration */}
          <div className="mb-4">
            <label className="flex items-center text-sm font-medium text-[#333333] mb-2">
              <FileText size={16} className="mr-2 text-[#009639]" />
              Registration Number (Optional)
            </label>
            <input
              type="text"
              value={vehicleRegistration}
              onChange={(e) => setVehicleRegistration(e.target.value)}
              placeholder="Enter registration number"
              className="w-full px-4 py-3 border border-[#e5e5e5] rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
            />
          </div>

          {/* Manufacturing Year */}
          <div className="mb-4">
            <label className="flex items-center text-sm font-medium text-[#333333] mb-2">
              <Calendar size={16} className="mr-2 text-[#009639]" />
              Manufacturing Year
            </label>
            <input
              type="number"
              value={manufacturingYear}
              onChange={(e) => setManufacturingYear(parseInt(e.target.value) || new Date().getFullYear())}
              min="1990"
              max={new Date().getFullYear() + 1}
              className="w-full px-4 py-3 border border-[#e5e5e5] rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
            />
          </div>

          {/* Color */}
          <div className="mb-4">
            <label className="flex items-center text-sm font-medium text-[#333333] mb-2">
              <Palette size={16} className="mr-2 text-[#009639]" />
              Color (Optional)
            </label>
            <input
              type="text"
              value={color}
              onChange={(e) => setColor(e.target.value)}
              placeholder="Enter vehicle color"
              className="w-full px-4 py-3 border border-[#e5e5e5] rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
            />
          </div>
        </div>

        {/* Member Invitations */}
        <div className="bg-white rounded-xl shadow-md border border-gray-100 p-6 mb-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold text-[#333333]">
              Invite Members
            </h2>
          </div>

          {/* Add Member Form */}
          <div className="rounded-lg mb-4">
            <div className="grid grid-cols-2 gap-3 mb-3">
              <input
                type="text"
                value={newMemberFirstName}
                onChange={(e) => setNewMemberFirstName(e.target.value)}
                placeholder="First name"
                className="px-3 py-2 border border-[#e5e5e5] rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
              />
              <input
                type="text"
                value={newMemberLastName}
                onChange={(e) => setNewMemberLastName(e.target.value)}
                placeholder="Last name"
                className="px-3 py-2 border border-[#e5e5e5] rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
              />
            </div>
            <div className="grid grid-cols-3 gap-3 mb-3">
              <input
                type="email"
                value={newMemberEmail}
                onChange={(e) => setNewMemberEmail(e.target.value)}
                placeholder="Email address"
                className="col-span-2 px-3 py-2 border border-[#e5e5e5] rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
              />
              <select
                value={newMemberRole}
                onChange={(e) => setNewMemberRole(e.target.value as GroupRoleEnum)}
                className="px-3 py-2 border border-[#e5e5e5] rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
              >
                <option value={GroupRoleEnum.MEMBER}>Member</option>
                <option value={GroupRoleEnum.ADMIN}>Admin</option>
              </select>
            </div>
            <button
              onClick={addMemberInvitation}
              disabled={
                !newMemberEmail.trim() ||
                !newMemberFirstName.trim() ||
                !newMemberLastName.trim()
              }
              className="w-full bg-[#009639] text-white py-2 rounded-full font-medium disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center justify-center"
            >
              <Plus size={16} className="mr-2" />
              Add Member
            </button>
          </div>

          {/* Member List */}
          {memberInvitations.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-[#333333] mb-2">
                Members to Invite ({memberInvitations.length})
              </h4>
              {memberInvitations.map((member) => (
                <div
                  key={member.id}
                  className="flex items-center justify-between p-3 bg-[#f8f9fa] rounded-lg"
                >
                  <div className="flex-1">
                    <p className="font-medium text-[#333333]">
                      {member.firstName} {member.lastName}
                    </p>
                    <p className="text-sm text-[#797879]">{member.email}</p>
                  </div>
                  <div className="flex items-center">
                    <span className="text-sm font-medium text-[#009639] mr-3">
                      {member.role}
                    </span>
                    <button
                      onClick={() => removeMemberInvitation(member.id)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <X size={16} />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Create Button */}
        <div className="bottom-0 left-0 right-0 p-4 bg-white z-50">
          <button
            onClick={handleCreateGroup}
            disabled={
              creating ||
              !groupName.trim() ||
              !description.trim() ||
              !selectedCityId ||
              !vinNumber.trim() ||
              !selectedModelId ||
              !currentUserPartyId
            }
            className="w-full bg-[#009639] text-white py-4 rounded-full font-semibold shadow-md disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center justify-center"
          >
            {creating ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                Creating Group...
              </>
            ) : (
              "Create Group"
            )}
          </button>
        </div>
      </div>
    </div>
  );
}