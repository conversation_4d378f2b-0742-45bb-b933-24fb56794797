"use client";

import BottomNavigationSPA from "@/components/navigation/BottomNavigationSPA";
import <PERSON>Renderer from "@/components/navigation/ScreenRenderer";
import SWRProvider from "@/components/SWRProvider";
import ErrorBoundary from "@/components/error-boundary";
import SupportChatDrawer from "@/components/SupportChatDrawer";
import { useSupportChatDrawerStore } from "@/lib/supportChatDrawerStore";
import outputs from "@/amplify_outputs.json";
import { CookieStorage } from "aws-amplify/utils";
import { Amplify } from "aws-amplify";
import { cognitoUserPoolsTokenProvider, resetPassword,  } from "aws-amplify/auth/cognito";
import { Authenticator, useTheme, Theme, ThemeProvider, useAuthenticator } from '@aws-amplify/ui-react';
import { ArrowLeft } from 'lucide-react';


Amplify.configure(outputs);

cognitoUserPoolsTokenProvider.setKeyValueStorage(new CookieStorage());

// Custom theme for borderless design with green colors
const createCustomTheme = (): Theme => {
  return {
    name: 'Poolly Auth Theme',
    tokens: {
      colors: {
        background: {
          primary: {
            value: 'white'
          },
        },
      },
      components: {
        authenticator: {
          router: {
            boxShadow: '0 0 0px transparent',
            borderWidth: '0',
          },
        },
        button: {
          primary: {
            backgroundColor: { value: '#009639' },
            color: { value: 'white' },
            borderWidth: '0',
            _hover: {
              backgroundColor: { value: '#007A2F' },
              color: { value: 'white' }
            },
            _active: {
              backgroundColor: { value: '#006225' },
              color: { value: 'white' }
            }
          },
          link: {
            color: { value: '#009639' },
            _hover: {
              color: { value: '#007A2F' }
            }
          }
        },
        tabs: {
          item: {
            color: { value: '#333333' },
            _active: {
              borderColor: { value: '#009639' },
              color: { value: '#009639' },
            },
          },
        },
      },
    }
  };
};

const formFields = {
  signUp: {
    given_name: {
      order: 1,
      placeholder: 'Enter your first name',
      label: 'First Name',
      required: true,
    },
    family_name: {
      order: 2,
      placeholder: 'Enter your last name',
      label: 'Last Name',
      required: true,
    },
    email: {
      order: 3,
      placeholder: 'Enter your email address',
      label: 'Email Address',
      required: true,
    },
    phone_number: {
      order: 4,
      placeholder: 'Enter your phone number',
      label: 'Phone Number',
      required: true,
    },
    password: {
      order: 5,
      placeholder: 'Enter your password',
      label: 'Password',
      required: true,
    },
    confirm_password: {
      order: 6,
      placeholder: 'Confirm your password',
      label: 'Confirm Password',
      required: true,
    },
  },
  signIn: {
    username: {
      placeholder: 'Enter your email',
      label: 'Email',
    },
    password: {
      placeholder: 'Enter your password',
      label: 'Password',
    },
  },
};

const components = {
  SignUp: {
    
    Footer() {
      return (
        <>
          {/* Password requirements text */}
          <div style={{
            fontSize: '0.875rem',
            color: '#6b7280',
            marginTop: '0.5rem',
            marginBottom: '1.5rem',
            fontFamily: 'Poppins, Arial, Helvetica, sans-serif',
            lineHeight: '1.4'
          }}>
            Password must be at least 8 characters long with a mix of letters, numbers, and symbols.
          </div>
          
          {/* Terms and Privacy Policy */}
          <div style={{
            display: 'flex',
            alignItems: 'flex-start',
            gap: '0.75rem',
            marginBottom: '1.5rem',
            fontSize: '0.875rem',
            color: '#6b7280',
            fontFamily: 'Poppins, Arial, Helvetica, sans-serif',
            lineHeight: '1.5'
          }}>
            <input
              type="checkbox"
              required
              style={{
                width: '16px',
                height: '16px',
                marginTop: '2px',
                accentColor: '#009639'
              }}
            />
            <label style={{ cursor: 'pointer' }}>
              I agree to the{' '}
              <a 
                href="/terms" 
                style={{ 
                  color: '#009639', 
                  textDecoration: 'none',
                  fontWeight: '500'
                }}
                onMouseEnter={(e) => (e.target as HTMLAnchorElement).style.textDecoration = 'underline'}
                onMouseLeave={(e) => (e.target as HTMLAnchorElement).style.textDecoration = 'none'}
              >
                Terms of Service
              </a>
              {' '}and{' '}
              <a 
                href="/privacy" 
                style={{ 
                  color: '#009639', 
                  textDecoration: 'none',
                  fontWeight: '500'
                }}
                onMouseEnter={(e) => (e.target as HTMLAnchorElement).style.textDecoration = 'underline'}
                onMouseLeave={(e) => (e.target as HTMLAnchorElement).style.textDecoration = 'none'}
              >
                Privacy Policy
              </a>
            </label>
          </div>

          {/* Footer link */}
   
        </>
      );
    },
  },
  SignIn: {
    
    Footer() {
      const { toForgotPassword } = useAuthenticator();
      return (
        <>
          {/* Additional spacing and content to match signup page */}
          <div style={{
            fontSize: '0.875rem',
            color: '#6b7280',
            marginTop: '0.5rem',
            marginBottom: '1.5rem',
            fontFamily: 'Poppins, Arial, Helvetica, sans-serif',
            lineHeight: '1.4',
            textAlign: 'center'
          }}>
            Welcome back! Please log in to continue.
          </div>
          
          {/* Forgot password link with more prominence */}
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            marginBottom: '2rem',
            fontSize: '0.875rem',
            fontFamily: 'Poppins, Arial, Helvetica, sans-serif',
          }}>
            <a 
              href="#" 
              style={{ 
                color: '#009639', 
                textDecoration: 'none',
                fontWeight: '500'
              }}
              onMouseEnter={(e) => (e.target as HTMLAnchorElement).style.textDecoration = 'underline'}
              onMouseLeave={(e) => (e.target as HTMLAnchorElement).style.textDecoration = 'none'}
              onClick={() => { toForgotPassword() }}
            >
              Forgot your password?
            </a>
          </div>

          {/* Privacy notice similar to signup */}
          <div style={{
            fontSize: '0.875rem',
            color: '#6b7280',
            marginBottom: '2rem',
            fontFamily: 'Poppins, Arial, Helvetica, sans-serif',
            lineHeight: '1.5',
            textAlign: 'center'
          }}>
            By logging in, you agree to our{' '}
            <a 
              href="/terms" 
              style={{ 
                color: '#009639', 
                textDecoration: 'none',
                fontWeight: '500'
              }}
              onMouseEnter={(e) => (e.target as HTMLAnchorElement).style.textDecoration = 'underline'}
              onMouseLeave={(e) => (e.target as HTMLAnchorElement).style.textDecoration = 'none'}
            >
              Terms of Service
            </a>
            {' '}and{' '}
            <a 
              href="/privacy" 
              style={{ 
                color: '#009639', 
                textDecoration: 'none',
                fontWeight: '500'
              }}
              onMouseEnter={(e) => (e.target as HTMLAnchorElement).style.textDecoration = 'underline'}
              onMouseLeave={(e) => (e.target as HTMLAnchorElement).style.textDecoration = 'none'}
            >
              Privacy Policy
            </a>
          </div>

          {/* Footer link */}
          <div style={{ 
            textAlign: 'center', 
            marginTop: '2rem',
            fontFamily: 'Poppins, Arial, Helvetica, sans-serif',
            color: '#6b7280'
          }}>
            
          </div>
        </>
      );
    },
    forgotPassword: {
      header: 'Reset your password',
      description: 'Enter your email to reset your password.',
      buttonLabel: 'Reset Password',
      
    },
  },
};

const text = {
  'Sign In': 'Log In',
  'Sign Up': 'Create Account',
  'Create Account': 'Sign Up',
  'Forgot your password?': 'Forgot password?',
  'Reset password': 'Reset Password',
  'Back to Sign In': 'Back to Log In',
  'Send code': 'Send Code',
  'Confirm': 'Verify',
  'Skip': 'Skip for now',
};

export default function MainLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const customTheme = createCustomTheme();
  const { isOpen: isSupportChatOpen, close: closeSupportChat } = useSupportChatDrawerStore();

  return (
    <ThemeProvider theme={customTheme}>
      <Authenticator 
        hideSignUp={false}
        formFields={formFields}
        components={components}
        initialState="signUp"
      >
        {({ signOut, user }) => (
          <ErrorBoundary showNavigation={false}>
            <SWRProvider>
              <div className="h-screen flex flex-col bg-white">
                {/* Main content area */}
                <div className="flex-1 overflow-hidden">
                  <ErrorBoundary showNavigation={true}>
                    <ScreenRenderer />
                  </ErrorBoundary>
                </div>
                
                {/* Bottom navigation */}
                <BottomNavigationSPA />

                {/* Support Chat Drawer */}
                <SupportChatDrawer isOpen={isSupportChatOpen} onClose={closeSupportChat} />
              </div>
            </SWRProvider>
          </ErrorBoundary>
        )}
      </Authenticator>
    </ThemeProvider>
  );
}
