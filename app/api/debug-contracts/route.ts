import { NextRequest, NextResponse } from "next/server";
import { db } from "@/db";
import { h_assignment_contracts } from "@/drizzle/h_schema/payment-contract";

export async function GET(request: NextRequest) {
  try {
    // Get all contracts in the database
    const contracts = await db
      .select({
        id: h_assignment_contracts.id,
        assignmentId: h_assignment_contracts.assignmentId,
        contractFilePath: h_assignment_contracts.contractFilePath,
        originalFilename: h_assignment_contracts.originalFilename,
        uploadedAt: h_assignment_contracts.uploadedAt,
        uploadedBy: h_assignment_contracts.uploadedBy,
      })
      .from(h_assignment_contracts)
      .orderBy(h_assignment_contracts.uploadedAt);

    return NextResponse.json({
      success: true,
      count: contracts.length,
      contracts: contracts,
    });
  } catch (error) {
    console.error("Error fetching contracts:", error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
}
