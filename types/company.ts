export interface CompanyBase {
  party_id: number;
  registration_number?: string | null;
  registration_country?: string | null;
  registration_date?: string | null;
  name: string;
  description?: string | null;
  city_id?: number | null;
  country_id?: number | null;
  purpose?: CompanyPurposeEnum | null;

}

export interface CompanyCreate extends CompanyBase {}

export interface CompanyRead extends CompanyBase {
  id: number;
  created_at: string;
  updated_at: string;
}
export interface CompanyUpdate extends CompanyBase {
  id: number;
}

export enum CompanyPurposeEnum {
  RIDE_SHARE = 'RIDE_SHARE',
  GROUP_MONETIZATION = 'GROUP_MONETIZATION',
  FLEET = 'FLEET',
  OTHER = 'OTHER',
  EHAILING_DRIVER = 'EHAILING_DRIVER',
}
