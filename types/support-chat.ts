export interface SupportChatWithDetails {
  id: number;
  driverPartyId: number;
  adminPartyId?: number | null;
  vehicleId?: number;
  type: "vehicle" | "general";
  subject: string;
  status: "active" | "resolved" | "closed";
  priority?: "low" | "normal" | "high" | "urgent" | null;
  lastMessageAt?: string | null;
  createdAt: string;
  updatedAt: string;
  // Relations
  driver?: {
    id: number;
    firstName?: string;
    lastName?: string;
    email?: string;
  };
  admin?: {
    id: number;
    firstName?: string;
    lastName?: string;
    email?: string;
  };
  vehicle?: {
    id: number;
    make: string;
    model: string;
    year: number;
    licensePlate?: string;
  };
  unreadCount?: number;
  lastMessage?: {
    id: number;
    content: string;
    senderPartyId: number;
    messageType: "text" | "image" | "document" | "system";
    createdAt: string;
  };
}

export interface SupportChatMessageWithSender {
  id: number;
  chatId: number;
  senderPartyId: number;
  messageType: "text" | "image" | "document" | "system";
  content: string;
  metadata?: any;
  isRead: boolean;
  readAt?: string;
  createdAt: string;
  updatedAt: string;
  // Relations
  sender?: {
    id: number;
    firstName?: string;
    lastName?: string;
    email?: string;
    role: "driver" | "admin";
  };
}

export interface CreateSupportChatData {
  type: "vehicle" | "general";
  subject: string;
  vehicleId?: number;
  initialMessage: string;
}

export interface SendMessageData {
  chatId: number;
  content: string;
  messageType?: "text" | "image" | "document";
  metadata?: any;
}

export interface SupportChatFilters {
  status?: "active" | "resolved" | "closed";
  type?: "vehicle" | "general";
  priority?: "low" | "normal" | "high" | "urgent";
  driverId?: number;
  adminId?: number;
  search?: string;
}