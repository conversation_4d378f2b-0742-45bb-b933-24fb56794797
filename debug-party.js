// Debug script to check party table and user's party_id
import { Pool } from 'pg';
import dotenv from 'dotenv';
dotenv.config();

async function checkPartyData() {
  const pool = new Pool({
    connectionString: process.env.DATABASE_URL,
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : undefined,
  });

  try {
    console.log('🔍 Checking party table for party_id = 26...');
    
    // First check party table structure
    console.log('\n📋 Party table structure:');
    const partyStructure = await pool.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns
      WHERE table_name = 'party'
      ORDER BY ordinal_position;
    `);

    partyStructure.rows.forEach(col => {
      console.log(`${col.column_name.padEnd(20)} | ${col.data_type.padEnd(15)} | ${col.is_nullable === 'YES' ? 'NULL' : 'NOT NULL'}`);
    });

    // Check if party_id 26 exists
    const partyCheck = await pool.query(`
      SELECT *
      FROM party
      WHERE id = 26;
    `);

    console.log('\n📋 Party ID 26 exists:', partyCheck.rows.length > 0);
    if (partyCheck.rows.length > 0) {
      console.log('Party details:', partyCheck.rows[0]);
    }

    // Check recent parties to see what IDs exist
    console.log('\n📋 Recent parties in the system:');
    const recentParties = await pool.query(`
      SELECT *
      FROM party
      ORDER BY id DESC
      LIMIT 10;
    `);

    recentParties.rows.forEach(party => {
      console.log(`ID: ${party.id} | Created: ${party.created_at || 'N/A'} | Updated: ${party.updated_at || 'N/A'}`);
    });

    // Check if there are any parties at all
    const totalParties = await pool.query(`SELECT COUNT(*) as count FROM party;`);
    console.log(`\n📊 Total parties in system: ${totalParties.rows[0].count}`);

    // Check the foreign key constraint
    console.log('\n🔗 Checking foreign key constraint:');
    const constraintCheck = await pool.query(`
      SELECT 
        tc.constraint_name, 
        tc.table_name, 
        kcu.column_name, 
        ccu.table_name AS foreign_table_name,
        ccu.column_name AS foreign_column_name 
      FROM 
        information_schema.table_constraints AS tc 
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
      WHERE tc.constraint_type = 'FOREIGN KEY' 
        AND tc.table_name = 'h_vehicle_catalog'
        AND kcu.column_name = 'party_id';
    `);

    if (constraintCheck.rows.length > 0) {
      console.log('Foreign key constraint details:', constraintCheck.rows[0]);
    } else {
      console.log('❌ No foreign key constraint found for party_id');
    }

  } catch (error) {
    console.error('❌ Error checking party data:', error.message);
  } finally {
    await pool.end();
  }
}

checkPartyData();
