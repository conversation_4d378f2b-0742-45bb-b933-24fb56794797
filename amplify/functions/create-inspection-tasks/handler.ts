import { <PERSON><PERSON> } from "aws-lambda";
import { Pool, PoolClient } from "pg";
import {
  SecretsManagerClient,
  GetSecretValueCommand,
} from "@aws-sdk/client-secrets-manager";

const secretsManager = new SecretsManagerClient({
  region: process.env.AWS_REGION || "eu-west-1",
});

async function getDatabaseCredentials(secretId: string) {
  const command = new GetSecretValueCommand({ SecretId: secretId });
  const response = await secretsManager.send(command);

  if (!response.SecretString) {
    throw new Error("Secret string is empty");
  }
  return JSON.parse(response.SecretString);
}

async function createPool() {
  const secret = await getDatabaseCredentials(process.env.SECRET_NAME || "");

  return new Pool({
    host: secret.POSTGRES_HOST,
    user: secret.POSTGRES_USER,
    password: secret.POSTGRES_PASSWORD,
    database: secret.POSTGRES_DB,
    port: secret.port || 5432,
    ssl: { rejectUnauthorized: false },
  });
}

export const handler: Handler = async (event, context) => {
  let pool: Pool | undefined;
  let client: PoolClient | undefined;

  try {
    pool = await createPool();
    pool.on("error", (err) => {
      console.error("Unexpected error on idle client", err);
    });
    client = await pool.connect();
    console.log("Connected to database!");

    // Query active assignments to get driver and vehicle information
    const assignmentsQuery = `
      SELECT 
        ha.id as assignment_id,
        ha.driver_party_id,
        ha.vehicle_id,
        ha.group_id,
        cp.value as driver_email,
        ind.first_name as driver_first_name,
        ind.last_name as driver_last_name,
        v.vehicle_registration,
        vm.model as vehicle_model,
        vmk.name as vehicle_make
      FROM h_assignments ha
      INNER JOIN party p ON ha.driver_party_id = p.id
      INNER JOIN contact_point cp ON p.id = cp.party_id AND cp.contact_point_type_id = 1 AND cp.is_primary = true
      LEFT JOIN individual ind ON p.id = ind.party_id
      LEFT JOIN vehicles v ON ha.vehicle_id = v.id
      LEFT JOIN vehicle_model vm ON v.model_id = vm.id
      LEFT JOIN vehicle_make vmk ON vm.make_id = vmk.id
      WHERE ha.status = 'active'
      AND ha.effective_to > NOW()
    `;

    const assignmentsResult = await client.query(assignmentsQuery);
    console.log(`Found ${assignmentsResult.rows.length} active assignments`);

    let tasksCreated = 0;
    let tasksSkipped = 0;

    // Process each assignment
    for (const assignment of assignmentsResult.rows) {
      // Check if a similar task already exists for this week to avoid duplicates
      const existingTaskQuery = `
        SELECT id FROM tasks 
        WHERE type = 'MANDATORY_REGULAR_VEHICLE_INSPECTION'
        AND party_id = $1
        AND related_entity_id = $2
        AND status IN ('PENDING', 'IN_PROGRESS')
        AND created_at >= DATE_TRUNC('week', NOW())
      `;

      const existingTaskResult = await client.query(existingTaskQuery, [
        assignment.driver_party_id,
        assignment.vehicle_id
      ]);

      if (existingTaskResult.rows.length > 0) {
        console.log(`Skipping task creation for assignment ${assignment.assignment_id} - task already exists this week`);
        tasksSkipped++;
        continue;
      }

      // Create the vehicle inspection task
      const vehicleName = assignment.vehicle_make && assignment.vehicle_model 
        ? `${assignment.vehicle_make} ${assignment.vehicle_model}` 
        : assignment.vehicle_registration || `Vehicle ID ${assignment.vehicle_id}`;

      const driverName = assignment.driver_first_name && assignment.driver_last_name
        ? `${assignment.driver_first_name} ${assignment.driver_last_name}`
        : "Driver";

      const insertTaskQuery = `
        INSERT INTO tasks (
          type,
          status,
          priority,
          title,
          description,
          email,
          party_id,
          related_entity_id,
          metadata,
          estimated_minutes,
          expires_at,
          created_at
        ) VALUES (
          'MANDATORY_REGULAR_VEHICLE_INSPECTION',
          'PENDING',
          'NORMAL',
          $1,
          $2,
          $3,
          $4,
          $5,
          $6,
          $7,
          $8,
          NOW()
        )
      `;

      const taskTitle = `Weekly Vehicle Inspection - ${vehicleName}`;
      const taskDescription = `Please complete the mandatory weekly inspection for ${vehicleName}. This includes checking tire condition, fluid levels, lights, and overall vehicle safety.`;
      
      const taskMetadata = {
        assignmentId: assignment.assignment_id,
        vehicleId: assignment.vehicle_id,
        vehicleName: vehicleName,
        vehicleRegistration: assignment.vehicle_registration,
        driverName: driverName,
        groupId: assignment.group_id,
        inspectionWeek: new Date().toISOString().split('T')[0]
      };

      const estimatedMinutes = 15; // Estimated time for vehicle inspection
      const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(); // Expires in 7 days

      await client.query(insertTaskQuery, [
        taskTitle,
        taskDescription,
        assignment.driver_email,
        assignment.driver_party_id,
        assignment.vehicle_id,
        JSON.stringify(taskMetadata),
        estimatedMinutes,
        expiresAt
      ]);

      console.log(`Created inspection task for ${driverName} - ${vehicleName}`);
      tasksCreated++;
    }

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: `Successfully processed ${assignmentsResult.rows.length} assignments`,
        tasksCreated,
        tasksSkipped,
        totalAssignments: assignmentsResult.rows.length
      }),
    };

  } catch (error) {
    console.error("Error:", error);
    return {
      statusCode: 500,
      body: JSON.stringify({
        message: "Error creating inspection tasks",
        error: (error as Error).message,
      }),
    };
  } finally {
    if (client) client.release();
    if (pool) await pool.end();
  }
};