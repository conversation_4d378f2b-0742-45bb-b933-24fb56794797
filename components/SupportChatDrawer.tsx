"use client";
import { useState, useEffect, useRef } from "react";
import { 
  <PERSON>Lef<PERSON>, 
  Send, 
  ArrowRight,
  Clock,
  MessageSquare,
  Check,
  CheckCheck,
  X,
  Plus,
  Menu,
  Search,
  HelpCircle,
  Car,
  DollarSign,
  Shield
} from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useUserAttributes } from "@/hooks/useUserAttributes";
import { 
  useSupportChats, 
  useSupportChatMessages, 
  useSupportChatActions,
  useSoundNotification 
} from "@/hooks/useSupportChat";
import type { SupportChatWithDetails, SupportChatMessageWithSender } from "@/types/support-chat";

interface SupportChatDrawerProps {
  isOpen: boolean;
  onClose: () => void;
}



export default function SupportChatDrawer({ isOpen, onClose }: SupportChatDrawerProps) {
  const [currentView, setCurrentView] = useState<"list" | "chat">("list");
  const [selectedChat, setSelectedChat] = useState<SupportChatWithDetails | null>(null);
  const [message, setMessage] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [optimisticMessages, setOptimisticMessages] = useState<any[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  const { attributes } = useUserAttributes();
  const currentUserPartyId = attributes ? parseInt(attributes["custom:db_id"] || "0") : 0;
  
  // SWR hooks for real-time data
  const { chats, isLoading: chatsLoading, mutate: mutateChats, totalUnread } = useSupportChats({
    driverId: currentUserPartyId || undefined
  });
  
  // Only fetch messages for real chats (numeric IDs), not optimistic chats (string IDs)
  const chatIdForMessages = selectedChat?.id && typeof selectedChat.id === 'number' ? selectedChat.id : null;
  const { messages, isLoading: messagesLoading, mutate: mutateMessages } = useSupportChatMessages(
    chatIdForMessages
  );
  

  
  // Chat actions
  const { createChat, sendMessage: sendChatMessage, markAsRead } = useSupportChatActions();
  
  // Sound notifications
  const { checkForNewMessages } = useSoundNotification();

  // Check for new messages and play sound notification
  useEffect(() => {
    if (messages.length > 0 && currentUserPartyId) {
      checkForNewMessages(messages, currentUserPartyId);
    }
  }, [messages, currentUserPartyId, checkForNewMessages]);

  useEffect(() => {
    if (currentView === "chat" && selectedChat) {
      scrollToBottom();
    }
  }, [currentView, selectedChat]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const handleBack = () => {
    switch (currentView) {
      case "chat":
        setCurrentView("list");
        setSelectedChat(null);
        break;
      
      default:
        onClose();
    }
  };

  const handleStartChat = async () => {
    if (!currentUserPartyId) {
      alert("Unable to start chat. Please try refreshing the page.");
      return;
    }

    // Create optimistic chat object and show immediately
    const tempChatId = `temp-chat-${Date.now()}`;
    const optimisticChat = {
      id: tempChatId,
      subject: "General Support",
      status: "active" as const,
      type: "general" as const,
      driverPartyId: currentUserPartyId,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isOptimistic: true, // Flag to identify this as optimistic
    };

    // Show chat screen immediately
    setSelectedChat(optimisticChat as any);
    setCurrentView("chat");

    // Create chat in background
    try {
      const result = await createChat({
        type: "general",
        subject: "General Support",
        vehicleId: undefined,
        initialMessage: "Hi, I need help with this issue.",
        driverPartyId: currentUserPartyId,
      });

      if (result.success) {
        // Replace optimistic chat with real chat
        await mutateChats();
        const newChat = chats.find(chat => chat.id === result.chatId);
        if (newChat) {
          setSelectedChat(newChat);
        }
      } else {
        // If creation failed, show error but keep the optimistic chat
        console.error("Failed to create chat:", result.error);
        // Could show a banner or toast notification here
      }
    } catch (error) {
      console.error("Failed to create chat:", error);
      // Could show a banner or toast notification here
    }
  };

  const handleSendMessage = async () => {
    if (!message.trim() || !selectedChat || !currentUserPartyId) return;

    const messageContent = message.trim();
    const tempId = `temp-${Date.now()}`;
    
    // Immediately add optimistic message to UI
    const optimisticMessage = {
      id: tempId,
      chatId: selectedChat.id,
      senderPartyId: currentUserPartyId,
      messageType: "text" as const,
      content: messageContent,
      createdAt: new Date().toISOString(),
      isRead: false,
      isPending: true, // Flag to indicate this is a pending message
    };

    setOptimisticMessages(prev => [...prev, optimisticMessage]);
    setMessage(""); // Clear input immediately

    try {
      const result = await sendChatMessage({
        chatId: selectedChat.id,
        content: messageContent,
        messageType: "text",
        senderPartyId: currentUserPartyId,
      });

      if (result.success) {
        // Remove optimistic message and refresh to get real message
        setOptimisticMessages(prev => prev.filter(msg => msg.id !== tempId));
        mutateMessages();
        mutateChats(); // Also refresh chats to update last message time
      } else {
        // Mark optimistic message as failed
        setOptimisticMessages(prev => 
          prev.map(msg => 
            msg.id === tempId 
              ? { ...msg, isPending: false, isFailed: true }
              : msg
          )
        );
        console.error("Failed to send message:", result.error);
      }
    } catch (error) {
      // Mark optimistic message as failed
      setOptimisticMessages(prev => 
        prev.map(msg => 
          msg.id === tempId 
            ? { ...msg, isPending: false, isFailed: true }
            : msg
        )
      );
      console.error("Error sending message:", error);
    }
  };

  const formatTime = (timestamp: string | Date) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const formatDate = (timestamp: string | Date) => {
    const today = new Date();
    const messageDate = new Date(timestamp);
    
    if (messageDate.toDateString() === today.toDateString()) {
      return "Today";
    } else if (messageDate.toDateString() === new Date(today.getTime() - 24 * 60 * 60 * 1000).toDateString()) {
      return "Yesterday";
    } else {
      return messageDate.toLocaleDateString();
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active": return "text-green-600";
      case "resolved": return "text-blue-600";
      case "closed": return "text-gray-500";
      default: return "text-gray-500";
    }
  };

  return (
    <>
      {/* Backdrop */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity duration-300"
          onClick={onClose}
        />
      )}
      
      {/* Drawer */}
      <div 
        className={`fixed top-0 right-0 h-full w-full max-w-md bg-white shadow-2xl z-50 transform transition-transform duration-300 ease-in-out ${
          isOpen ? 'translate-x-0' : 'translate-x-full'
        }`}
      >
        {/* Header */}
        <div className="bg-[#009639] px-6 py-4 text-left border-b-0">
          <div className="flex items-center">
            <button
              onClick={handleBack}
              className="mr-3 rounded-full p-1 text-white hover:bg-[#007A2F] transition-colors"
              title="Back"
            >
              <ArrowLeft size={24} />
            </button>
            <div>
              <h3 className="text-xl font-bold text-white">
                {currentView === "list" && "Support Chat"}
                {currentView === "chat" && (selectedChat?.subject || "Support Chat")}
              </h3>

            </div>
            {/* Action buttons */}
            <div className="ml-auto flex items-center space-x-2">
              {/* New Chat button - always visible */}
              <button
                onClick={handleStartChat}
                className="rounded-full p-1 text-white hover:bg-[#007A2F] transition-colors"
                title="New Chat"
              >
                <Plus size={24} />
              </button>
              
              {/* Burger Menu - only show in chat view */}
              {currentView === "chat" && (
                <button
                  onClick={() => {/* Add menu functionality here */}}
                  className="rounded-full p-1 text-white hover:bg-[#007A2F] transition-colors"
                  title="Menu"
                >
                  <Menu size={24} />
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto bg-white" style={{ height: 'calc(100vh - 76px)' }}>
          {/* List Landing */}
          {currentView === "list" && (
            <div className="p-4">
              {/* Search Bar */}
              <div className="relative mb-4">
                <input
                  type="text"
                  placeholder="Search or start a new chat"
                  className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#009639] focus:border-transparent"
                />
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
              </div>

              {/* Filter Tabs */}
              <div className="flex space-x-2 mb-6 overflow-x-auto">
                {["All", "Unread", "Favorites", "Groups", "Label"].map((tab) => (
                  <button
                    key={tab}
                    className={`px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap ${
                      tab === "All" 
                        ? "bg-gray-900 text-white" 
                        : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                    }`}
                  >
                    {tab}
                  </button>
                ))}
              </div>

              {/* Chat List */}
              <div className="space-y-3">
                {chatsLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#009639]"></div>
                  </div>
                ) : chats.length > 0 ? (
                  chats.map((chat) => {
                    const iconMap = {
                      general: HelpCircle,
                      vehicle: Car,
                      financial: DollarSign,
                      safety: Shield
                    };
                    const Icon = iconMap[chat.type] || HelpCircle;
                    
                    const colorMap = {
                      general: "bg-[#009639]",
                      vehicle: "bg-[#009639]", 
                      financial: "bg-yellow-500",
                      safety: "bg-red-500"
                    };
                    const bgColor = colorMap[chat.type] || "bg-[#009639]";

                    return (
                      <div
                        key={chat.id}
                        onClick={() => {
                          setSelectedChat(chat);
                          setCurrentView("chat");
                        }}
                        className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 cursor-pointer"
                      >
                        <div className={`w-12 h-12 rounded-full ${bgColor} flex items-center justify-center`}>
                          <Icon size={20} className="text-white" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between mb-1">
                            <h3 className="font-medium text-gray-900 capitalize">{chat.type}</h3>
                            <span className="text-sm text-gray-500">
                              {chat.lastMessageAt 
                                ? new Date(chat.lastMessageAt).toLocaleDateString() === new Date().toLocaleDateString()
                                  ? "Today"
                                  : new Date(chat.lastMessageAt).toLocaleDateString() === new Date(Date.now() - 86400000).toLocaleDateString()
                                  ? "Yesterday" 
                                  : new Date(chat.lastMessageAt).toLocaleDateString()
                                : "New"
                              }
                            </span>
                          </div>
                          <p className="text-sm text-gray-600 truncate">
                            {chat.subject || "No subject"}
                          </p>
                        </div>
                        {/* Unread indicator */}
                        <div className="flex items-center">
                          <div className="w-2 h-2 bg-[#009639] rounded-full"></div>
                        </div>
                      </div>
                    );
                  })
                ) : (
                  <div className="text-center py-8">
                    <MessageSquare size={48} className="mx-auto text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No conversations yet</h3>
                    <p className="text-gray-600 mb-4">Start a new conversation with our support team</p>
                    <button
                      onClick={handleStartChat}
                      className="bg-[#009639] text-white px-6 py-2 rounded-lg hover:bg-[#007A2F]"
                    >
                      Start New Chat
                    </button>
                  </div>
                )}
              </div>
            </div>
          )}





          {/* Chat Interface */}
          {currentView === "chat" && selectedChat && (
            <div className="flex flex-col" style={{ height: 'calc(100vh - 76px)' }}>
              {/* Chat Messages */}
              <div className="flex-1 overflow-y-auto p-4 space-y-3">
                {messagesLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#009639]"></div>
                  </div>
                ) : (
                  <>
                    {/* Show welcome message for new optimistic chats */}
                    {(selectedChat as any)?.isOptimistic && messages.length === 0 && optimisticMessages.length === 0 && (
                      <div className="flex justify-start">
                        <div className="max-w-[75%] rounded-2xl rounded-bl-md px-4 py-2 bg-white text-[#333333] shadow-sm">
                          <p className="text-xs text-[#009639] font-medium mb-1">Support</p>
                          <p className="text-sm leading-relaxed">
                            Hi! 👋 I'm here to help you with any questions or issues you might have. How can I assist you today?
                          </p>
                          <div className="flex items-center justify-end mt-1 space-x-1 text-[#797879]">
                            <span className="text-xs">{formatTime(new Date())}</span>
                          </div>
                        </div>
                      </div>
                    )}
                    {[...messages, ...optimisticMessages].map((msg, index, allMessages) => {
                    const isFromDriver = msg.senderPartyId === currentUserPartyId;
                    const showDate = index === 0 || 
                      formatDate(msg.createdAt) !== formatDate(allMessages[index - 1].createdAt);

                    return (
                      <div key={msg.id}>
                        {showDate && (
                          <div className="text-center py-2">
                            <span className="bg-white px-3 py-1 rounded-full text-xs text-[#797879] shadow-sm">
                              {formatDate(msg.createdAt)}
                            </span>
                          </div>
                        )}
                        <div className={`flex ${isFromDriver ? "justify-end" : "justify-start"}`}>
                          <div
                            className={`max-w-[75%] rounded-2xl px-4 py-2 ${
                              isFromDriver
                                ? "bg-[#009639] text-white rounded-br-md"
                                : "bg-white text-[#333333] rounded-bl-md shadow-sm"
                            }`}
                          >
                            {!isFromDriver && (
                              <p className="text-xs text-[#009639] font-medium mb-1">
                                Support
                              </p>
                            )}
                            <p className="text-sm leading-relaxed">{msg.content}</p>
                            <div className={`flex items-center justify-end mt-1 space-x-1 ${
                              isFromDriver ? "text-green-100" : "text-[#797879]"
                            }`}>
                              <span className="text-xs">{formatTime(msg.createdAt)}</span>
                              {isFromDriver && (
                                (msg as any).isPending ? (
                                  <div className="w-3 h-3 border border-green-200 border-t-transparent rounded-full animate-spin"></div>
                                ) : (msg as any).isFailed ? (
                                  <X size={12} className="text-red-200" />
                                ) : msg.isRead ? (
                                  <CheckCheck size={12} className="text-blue-200" />
                                ) : (
                                  <Check size={12} className="text-green-200" />
                                )
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                    })}
                  </>
                )}
                {isTyping && (
                  <div className="flex justify-start">
                    <div className="bg-white rounded-2xl rounded-bl-md px-4 py-2 shadow-sm">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-[#797879] rounded-full animate-pulse"></div>
                        <div className="w-2 h-2 bg-[#797879] rounded-full animate-pulse" style={{ animationDelay: "0.1s" }}></div>
                        <div className="w-2 h-2 bg-[#797879] rounded-full animate-pulse" style={{ animationDelay: "0.2s" }}></div>
                      </div>
                    </div>
                  </div>
                )}
                <div ref={messagesEndRef} />
              </div>

              {/* Message Input */}
              <div className="bg-white border-t border-gray-200 p-4">
                <div className="flex items-center space-x-3">
                  <Input
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    placeholder="Type a message..."
                    className="flex-1 border-gray-300 rounded-full px-4 py-2 focus:ring-[#009639] focus:border-[#009639]"
                    onKeyPress={(e) => e.key === "Enter" && handleSendMessage()}
                  />
                  <Button
                    onClick={handleSendMessage}
                    disabled={!message.trim()}
                    className="bg-[#009639] hover:bg-[#007A2F] text-white rounded-full p-2 w-10 h-10 flex items-center justify-center disabled:opacity-50"
                  >
                    <Send size={18} />
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
}