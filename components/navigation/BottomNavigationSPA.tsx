"use client";

import { useNavigationStore } from "@/lib/navigation/navigationStore";
import { useNavigation } from "@/hooks/useNavigation";
import { Home, Car, Compass, Users, CheckSquare, MessageSquare } from "lucide-react";
import { useSupportChatDrawerStore } from "@/lib/supportChatDrawerStore";

export default function BottomNavigationSPA() {
  const { currentScreen } = useNavigationStore();
  const { 
    navigateToHome, 
    navigateToVehicleDashboard, 
    navigateToOpportunities, 
    navigateToMyGroups, 
    navigateToTasks 
  } = useNavigation();

  const { open: openChatDrawer } = useSupportChatDrawerStore();

  const navItems = [
    {
      label: "Home",
      icon: Home,
      onPress: navigateToHome,
      active: currentScreen === "home",
    },
    {
      label: "Vehicles",
      icon: Car,
      onPress: navigateToVehicleDashboard,
      active: currentScreen === "vehicle-dashboard" || currentScreen.startsWith("vehicle-"),
    },
    {
      label: "Marketplace",
      icon: Compass,
      onPress: navigateToOpportunities,
      active: currentScreen === "opportunities" || currentScreen.includes("opportunity") || currentScreen.includes("marketplace"),
    },
    {
      label: "Groups",
      icon: Users,
      onPress: navigateToMyGroups,
      active: currentScreen === "my-groups" || currentScreen.startsWith("group-") || currentScreen.startsWith("member-") || currentScreen === "create-group" || currentScreen === "add-members",
    },
    {
      label: "Tasks",
      icon: CheckSquare,
      onPress: navigateToTasks,
      active: currentScreen === "tasks",
    },
    {
      label: "Chat",
      icon: MessageSquare,
      onPress: openChatDrawer,
      active: false,
    }
  ];

  return (
    <div className="bg-white border-t border-[#f2f2f2]">
      {/* Main navigation content */}
      <div className="flex items-center justify-around px-2 py-2 min-h-[68px]">
        {navItems.map((item) => (
          <button
            key={item.label}
            className={`flex flex-col items-center justify-center w-full h-full py-2 px-1 transition-colors duration-200 active:scale-95 ${
              item.active ? "text-[#009639]" : "text-[#797879]"
            }`}
            onClick={item.onPress}
          >
            <item.icon
              size={24}
              className={`transition-colors duration-200 ${item.active ? "text-[#009639]" : "text-[#797879]"}`}
            />
            <span className={`text-[10px] mt-1 font-medium transition-colors duration-200 ${
              item.active ? "text-[#009639]" : "text-[#797879]"
            }`}>
              {item.label}
            </span>
          </button>
        ))}
      </div>
      {/* Safe area spacer for iOS devices */}
      <div className="pb-safe-bottom"></div>
    </div>
  );
} 