"use server";

import { db } from "@/db";
import { supportChats, supportChatMessages, supportChatParticipants, party, individual } from "@/drizzle/schema";
import { eq, desc, and, ilike, isNull } from "drizzle-orm";
import { revalidatePath } from "next/cache";
import type { 
  SupportChatWithDetails, 
  SupportChatMessageWithSender, 
  CreateSupportChatData, 
  SendMessageData,
  SupportChatFilters 
} from "@/types/support-chat";

/**
 * Create a new support chat
 */
export async function createSupportChat(data: CreateSupportChatData & { driverPartyId: number }) {
  try {
    const result = await db.transaction(async (tx) => {
      // Create the chat
      const [chat] = await tx
        .insert(supportChats)
        .values({
          driverPartyId: data.driverPartyId,
          vehicleId: data.vehicleId,
          type: data.type,
          subject: data.subject,
          status: "active",
          priority: "normal",
          lastMessageAt: new Date().toISOString(),
        })
        .returning();

      // Add the driver as a participant
      await tx.insert(supportChatParticipants).values({
        chatId: chat.id,
        partyId: data.driverPartyId,
        role: "driver",
      });

      // Send the initial message
      await tx.insert(supportChatMessages).values({
        chatId: chat.id,
        senderPartyId: data.driverPartyId,
        messageType: "text",
        content: data.initialMessage,
      });

      return chat;
    });

    revalidatePath("/admin/support-chat");
    return { success: true, chatId: result.id };
  } catch (error) {
    console.error("Error creating support chat:", error);
    return { success: false, error: "Failed to create support chat" };
  }
}

/**
 * Send a message in a support chat
 */
export async function sendSupportChatMessage(data: SendMessageData & { senderPartyId: number }) {
  try {
    const result = await db.transaction(async (tx) => {
      // Insert the message
      const [message] = await tx
        .insert(supportChatMessages)
        .values({
          chatId: data.chatId,
          senderPartyId: data.senderPartyId,
          messageType: data.messageType || "text",
          content: data.content,
          metadata: data.metadata,
        })
        .returning();

      // Update the chat's last message timestamp
      await tx
        .update(supportChats)
        .set({ 
          lastMessageAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        })
        .where(eq(supportChats.id, data.chatId));

      return message;
    });

    revalidatePath("/admin/support-chat");
    return { success: true, messageId: result.id };
  } catch (error) {
    console.error("Error sending support chat message:", error);
    return { success: false, error: "Failed to send message" };
  }
}

/**
 * Get support chats with filters
 */
export async function getSupportChats(filters: SupportChatFilters = {}) {
  try {
    const conditions = [];

    if (filters.status) {
      conditions.push(eq(supportChats.status, filters.status));
    }

    if (filters.type) {
      conditions.push(eq(supportChats.type, filters.type));
    }

    if (filters.driverId) {
      conditions.push(eq(supportChats.driverPartyId, filters.driverId));
    }

    if (filters.search) {
      conditions.push(ilike(supportChats.subject, `%${filters.search}%`));
    }

    let query = db.select().from(supportChats).$dynamic();
    
    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    const chats = await query
      .orderBy(desc(supportChats.lastMessageAt))
      .limit(50);

    // Convert to the expected format with basic structure
    const formattedChats = chats.map(chat => ({
      id: chat.id,
      driverPartyId: chat.driverPartyId,
      adminPartyId: chat.adminPartyId,
      vehicleId: chat.vehicleId,
      type: chat.type,
      subject: chat.subject,
      status: chat.status,
      priority: chat.priority,
      lastMessageAt: chat.lastMessageAt,
      createdAt: chat.createdAt,
      updatedAt: chat.updatedAt,
      unreadCount: 0, // Will be calculated separately if needed
      lastMessage: undefined, // Will be fetched separately if needed
    })) as SupportChatWithDetails[];

    return { success: true, chats: formattedChats };
  } catch (error) {
    console.error("Error getting support chats:", error);
    return { success: false, error: "Failed to get support chats" };
  }
}

/**
 * Get messages for a specific support chat with sender information
 */
export async function getSupportChatMessages(chatId: number) {
  try {
    // Get basic messages first
    const messages = await db
      .select()
      .from(supportChatMessages)
      .where(eq(supportChatMessages.chatId, chatId))
      .orderBy(supportChatMessages.createdAt);

    // Format messages with basic sender info for now
    const formattedMessages = messages.map(message => ({
      id: message.id,
      chatId: message.chatId,
      senderPartyId: message.senderPartyId,
      messageType: message.messageType,
      content: message.content,
      metadata: message.metadata,
      isRead: message.isRead,
      readAt: message.readAt,
      createdAt: message.createdAt,
      updatedAt: message.updatedAt,
      sender: {
        id: message.senderPartyId,
        firstName: "User",
        lastName: "",
        email: "",
        role: "driver" as const
      }
    })) as SupportChatMessageWithSender[];

    return { success: true, messages: formattedMessages };
  } catch (error) {
    console.error("Error getting support chat messages:", error);
    return { success: false, error: "Failed to get messages" };
  }
}

/**
 * Update support chat status
 */
export async function updateSupportChatStatus(
  chatId: number, 
  status: "active" | "resolved" | "closed",
  adminPartyId?: number
) {
  try {
    const updateData: any = {
      status,
      updatedAt: new Date().toISOString(),
    };

    // If an admin is updating the status, assign them to the chat
    if (adminPartyId) {
      updateData.adminPartyId = adminPartyId;
    }

    await db
      .update(supportChats)
      .set(updateData)
      .where(eq(supportChats.id, chatId));

    // Add admin as participant if not already
    if (adminPartyId) {
      try {
        await db.insert(supportChatParticipants).values({
          chatId,
          partyId: adminPartyId,
          role: "admin",
        });
      } catch (error) {
        // Ignore if already exists (unique constraint)
      }
    }

    revalidatePath("/admin/support-chat");
    return { success: true };
  } catch (error) {
    console.error("Error updating support chat status:", error);
    return { success: false, error: "Failed to update status" };
  }
}

/**
 * Mark messages as read
 */
export async function markMessagesAsRead(chatId: number, partyId: number) {
  try {
    await db
      .update(supportChatMessages)
      .set({ 
        isRead: true, 
        readAt: new Date().toISOString(),
      })
      .where(
        and(
          eq(supportChatMessages.chatId, chatId),
          // Only mark messages not sent by the current user as read
          // This prevents marking your own messages as "read"
        )
      );

    // Update participant's last read timestamp
    await db
      .update(supportChatParticipants)
      .set({ lastReadAt: new Date().toISOString() })
      .where(
        and(
          eq(supportChatParticipants.chatId, chatId),
          eq(supportChatParticipants.partyId, partyId)
        )
      );

    return { success: true };
  } catch (error) {
    console.error("Error marking messages as read:", error);
    return { success: false, error: "Failed to mark messages as read" };
  }
}

/**
 * Get driver's vehicles for chat creation (simplified)
 */
export async function getDriverVehicles(driverPartyId: number) {
  try {
    // Return empty for now - vehicles not needed for basic chat
    return { success: true, vehicles: [] };
  } catch (error) {
    return { success: true, vehicles: [] };
  }
}

/**
 * Get chat statistics for admin dashboard
 */
export async function getSupportChatStats() {
  try {
    const [totalChats] = await db
      .select({ count: supportChats.id })
      .from(supportChats);

    const [activeChats] = await db
      .select({ count: supportChats.id })
      .from(supportChats)
      .where(eq(supportChats.status, "active"));

    const [urgentChats] = await db
      .select({ count: supportChats.id })
      .from(supportChats)
      .where(
        and(
          eq(supportChats.status, "active"),
          eq(supportChats.priority, "urgent")
        )
      );

    const [unassignedChats] = await db
      .select({ count: supportChats.id })
      .from(supportChats)
      .where(
        and(
          eq(supportChats.status, "active"),
          isNull(supportChats.adminPartyId)
        )
      );

    return {
      success: true,
      stats: {
        total: totalChats?.count || 0,
        active: activeChats?.count || 0,
        urgent: urgentChats?.count || 0,
        unassigned: unassignedChats?.count || 0,
      },
    };
  } catch (error) {
    console.error("Error getting support chat stats:", error);
    return { success: false, error: "Failed to get stats" };
  }
}