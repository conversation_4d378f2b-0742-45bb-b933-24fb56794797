"use server";

import { revalidatePath } from "next/cache";
import {
  getAllPaymentRecords,
  getBasicAssignments,
  PaymentRecord,
  BasicAssignment,
} from "@/drizzle-actions/admin/payments";

/**
 * Server action to get all payment records
 */
export async function getAllPaymentRecordsAction(): Promise<{
  success: boolean;
  data?: PaymentRecord[];
  error?: string;
}> {
  try {
    const result = await getAllPaymentRecords();
    
    if (result.success) {
      // Revalidate the payments page to ensure fresh data
      revalidatePath("/admin/payments");
    }
    
    return result;
  } catch (error) {
    console.error("❌ [getAllPaymentRecordsAction] Error:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to fetch payment records",
    };
  }
}

/**
 * Server action to get basic assignment information
 */
export async function getBasicAssignmentsAction(): Promise<{
  success: boolean;
  data?: BasicAssignment[];
  error?: string;
}> {
  try {
    const result = await getBasicAssignments();
    
    return result;
  } catch (error) {
    console.error("❌ [getBasicAssignmentsAction] Error:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to fetch assignments",
    };
  }
}

/**
 * Server action to refresh payment data
 */
export async function refreshPaymentDataAction(): Promise<{
  success: boolean;
  message?: string;
  error?: string;
}> {
  try {
    // Revalidate the payments page to force fresh data fetch
    revalidatePath("/admin/payments");

    return {
      success: true,
      message: "Payment data refreshed successfully",
    };
  } catch (error) {
    console.error("❌ [refreshPaymentDataAction] Error:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to refresh payment data",
    };
  }
}

/**
 * Server action to test assignment loading (for debugging)
 */
export async function testAssignmentLoadingAction(): Promise<{
  success: boolean;
  data?: any;
  error?: string;
}> {
  try {
    const result = await getBasicAssignments();
    console.log("🧪 [testAssignmentLoadingAction] New h_assignments result:", result);

    // Also test what the assignments page uses
    const { getInventoryVehicleDriverAssignments } = await import("@/actions/admin/assignments");
    const assignmentsPageResult = await getInventoryVehicleDriverAssignments();
    console.log("🧪 [testAssignmentLoadingAction] Old assignments page result:", assignmentsPageResult);

    // Test the new assignment system
    let newAssignmentsResult = { success: false, data: [], error: "Not implemented yet" };
    try {
      const { getAllAssignmentsAction } = await import("@/actions/admin/assignments-new");
      newAssignmentsResult = await getAllAssignmentsAction();
      console.log("🧪 [testAssignmentLoadingAction] New assignments system result:", newAssignmentsResult);
    } catch (importError) {
      console.log("🧪 [testAssignmentLoadingAction] New assignments system not available yet:", importError);
    }

    return {
      success: true,
      data: {
        // Old system (payments trying to use groups/inventory)
        oldPaymentsAssignmentCount: result.data?.length || 0,
        oldPaymentsAssignments: result.data || [],

        // Old assignments page (inventory-based)
        oldAssignmentsPageCount: assignmentsPageResult.success ? assignmentsPageResult.data?.length || 0 : 0,
        oldAssignmentsPageData: assignmentsPageResult.success ? assignmentsPageResult.data : null,

        // New system (h_assignments table)
        newAssignmentsCount: newAssignmentsResult.success ? newAssignmentsResult.data?.length || 0 : 0,
        newAssignmentsData: newAssignmentsResult.success ? newAssignmentsResult.data : null,

        // Raw results
        oldPaymentsResult: result,
        oldAssignmentsPageResult: assignmentsPageResult,
        newAssignmentsResult,
      },
    };
  } catch (error) {
    console.error("❌ [testAssignmentLoadingAction] Error:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to test assignment loading",
    };
  }
}
