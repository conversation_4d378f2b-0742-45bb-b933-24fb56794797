"use server";

import { db } from "@/db";
import { 
  supportChats, 
  supportChatMessages, 
  supportChatParticipants,
  party,
  individual,
  contactPoint
} from "@/drizzle/schema";
import { eq, desc, and, or, ilike, sql, count } from "drizzle-orm";
import { getCurrentUserNoCache } from "@/lib/amplifyServerUtils";
import type { 
  SupportChatWithDetails, 
  SupportChatMessageWithSender,
  SendMessageData
} from "@/types/support-chat";

/**
 * Get current admin user's party ID
 */
async function getCurrentAdminPartyId(): Promise<number> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const partyId = parseInt(userAttributes?.["custom:db_id"] || "0");
    
    if (!partyId || isNaN(partyId)) {
      throw new Error("Admin user not properly authenticated or missing party ID");
    }
    
    return partyId;
  } catch (error) {
    console.error("Error getting admin party ID:", error);
    throw error;
  }
}

/**
 * Get all support chats for admin interface with customer details
 */
export async function getAdminSupportChats(filters: {
  status?: "active" | "resolved" | "closed";
  type?: "vehicle" | "general";
  search?: string;
  assignedToMe?: boolean;
} = {}) {
  try {
    const adminPartyId = await getCurrentAdminPartyId();
    
    // Query with last message and unread count
    let query = db
      .select({
        chat: supportChats,
        driverInfo: {
          id: individual.id,
          firstName: individual.firstName,
          lastName: individual.lastName,
          email: sql<string>`NULL`, // Will fetch email separately if needed
        },
        lastMessage: {
          id: sql<number>`last_msg.id`,
          content: sql<string>`last_msg.content`,
          messageType: sql<string>`last_msg.message_type`,
          createdAt: sql<string>`last_msg.created_at`,
          senderPartyId: sql<number>`last_msg.sender_party_id`
        },
        unreadCount: sql<number>`COALESCE(unread_count.count, 0)`
      })
      .from(supportChats)
      .leftJoin(party, eq(supportChats.driverPartyId, party.id))
      .leftJoin(individual, eq(party.id, individual.partyId))
      .leftJoin(
        sql`(
          SELECT DISTINCT ON (chat_id) 
            id, chat_id, content, message_type, created_at, sender_party_id
          FROM support_chat_messages 
          ORDER BY chat_id, created_at DESC
        ) last_msg`,
        sql`last_msg.chat_id = ${supportChats.id}`
      )
      .leftJoin(
        sql`(
          SELECT chat_id, COUNT(*) as count
          FROM support_chat_messages 
          WHERE is_read = false
          GROUP BY chat_id
        ) unread_count`,
        sql`unread_count.chat_id = ${supportChats.id}`
      )
      .groupBy(
        supportChats.id,
        individual.id,
        individual.firstName,
        individual.lastName,
        sql`last_msg.id`,
        sql`last_msg.content`,
        sql`last_msg.message_type`,
        sql`last_msg.created_at`,
        sql`last_msg.sender_party_id`,
        sql`unread_count.count`
      );

    // Apply filters
    const conditions = [];
    
    if (filters.status) {
      conditions.push(eq(supportChats.status, filters.status));
    }
    
    if (filters.type) {
      conditions.push(eq(supportChats.type, filters.type));
    }
    
    if (filters.search) {
      conditions.push(
        or(
          ilike(supportChats.subject, `%${filters.search}%`),
          ilike(individual.firstName, `%${filters.search}%`),
          ilike(individual.lastName, `%${filters.search}%`)
        )
      );
    }
    
    if (filters.assignedToMe) {
      conditions.push(eq(supportChats.adminPartyId, adminPartyId));
    }
    
    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }
    
    const results = await query
      .orderBy(desc(supportChats.lastMessageAt))
      .limit(50);
    
    return {
      success: true,
      chats: results.map(result => ({
        id: result.chat.id,
        driverPartyId: result.chat.driverPartyId,
        adminPartyId: result.chat.adminPartyId,
        vehicleId: result.chat.vehicleId,
        type: result.chat.type,
        subject: result.chat.subject,
        status: result.chat.status,
        priority: result.chat.priority,
        lastMessageAt: result.chat.lastMessageAt,
        createdAt: result.chat.createdAt,
        updatedAt: result.chat.updatedAt,
        driver: result.driverInfo,
        unreadCount: result.unreadCount || 0,
        lastMessage: result.lastMessage?.id ? {
          id: result.lastMessage.id,
          content: result.lastMessage.content,
          senderPartyId: result.lastMessage.senderPartyId,
          messageType: result.lastMessage.messageType as "text" | "image" | "document" | "system",
          createdAt: result.lastMessage.createdAt as string
        } : undefined,
      })) as SupportChatWithDetails[]
    };
  } catch (error) {
    console.error("Error fetching admin support chats:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to fetch chats",
      chats: []
    };
  }
}

/**
 * Get messages for a specific chat with sender details
 */
export async function getAdminSupportChatMessages(chatId: number) {
  try {
    const messages = await db
      .select({
        message: supportChatMessages,
        sender: {
          id: individual.id,
          firstName: individual.firstName,
          lastName: individual.lastName,
          email: "",
          isAdmin: sql<boolean>`
            CASE WHEN EXISTS (
              SELECT 1 FROM ${supportChatParticipants} 
              WHERE party_id = ${supportChatMessages.senderPartyId} 
              AND chat_id = ${chatId}
              AND role IN ('admin', 'supervisor')
            ) THEN true ELSE false END
          `
        }
      })
      .from(supportChatMessages)
      .leftJoin(party, eq(supportChatMessages.senderPartyId, party.id))
      .leftJoin(individual, eq(party.id, individual.partyId))
      .where(eq(supportChatMessages.chatId, chatId))
      .orderBy(supportChatMessages.createdAt);
    
    return {
      success: true,
      messages: messages.map(result => ({
        id: result.message.id,
        chatId: result.message.chatId,
        senderPartyId: result.message.senderPartyId,
        messageType: result.message.messageType,
        content: result.message.content,
        metadata: result.message.metadata,
        isRead: result.message.isRead,
        readAt: result.message.readAt,
        createdAt: result.message.createdAt,
        updatedAt: result.message.updatedAt,
        sender: {
          id: result.sender.id || 0,
          firstName: result.sender.firstName || "Unknown",
          lastName: result.sender.lastName || "User",
          email: "",
          role: result.sender.isAdmin ? "admin" as const : "driver" as const
        }
      })) as SupportChatMessageWithSender[]
    };
  } catch (error) {
    console.error("Error fetching chat messages:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to fetch messages",
      messages: []
    };
  }
}

/**
 * Send a message as admin in a support chat
 */
export async function sendAdminSupportMessage(data: {
  chatId: number;
  content: string;
  messageType?: "text" | "image" | "document";
}) {
  try {
    const adminPartyId = await getCurrentAdminPartyId();
    
    // Start transaction
    const result = await db.transaction(async (tx) => {
      // Insert the message
      const [message] = await tx
        .insert(supportChatMessages)
        .values({
          chatId: data.chatId,
          senderPartyId: adminPartyId,
          messageType: data.messageType || "text",
          content: data.content,
        })
        .returning();
      
      // Update chat's last message time and assign admin if not already assigned
      await tx
        .update(supportChats)
        .set({ 
          lastMessageAt: new Date().toISOString(),
          adminPartyId: adminPartyId, // Assign chat to this admin
          updatedAt: new Date().toISOString()
        })
        .where(eq(supportChats.id, data.chatId));
      
      // Ensure admin is a participant
      await tx
        .insert(supportChatParticipants)
        .values({
          chatId: data.chatId,
          partyId: adminPartyId,
          role: "admin",
        })
        .onConflictDoNothing(); // Don't fail if already exists
      
      return message;
    });
    
    return {
      success: true,
      messageId: result.id
    };
  } catch (error) {
    console.error("Error sending admin message:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to send message"
    };
  }
}

/**
 * Assign a chat to a specific admin
 */
export async function assignChatToAdmin(chatId: number, adminPartyId?: number) {
  try {
    const currentAdminId = adminPartyId || await getCurrentAdminPartyId();
    
    await db.transaction(async (tx) => {
      // Update chat assignment
      await tx
        .update(supportChats)
        .set({ 
          adminPartyId: currentAdminId,
          updatedAt: new Date().toISOString()
        })
        .where(eq(supportChats.id, chatId));
      
      // Ensure admin is a participant
      await tx
        .insert(supportChatParticipants)
        .values({
          chatId: chatId,
          partyId: currentAdminId,
          role: "admin",
        })
        .onConflictDoNothing();
    });
    
    return { success: true };
  } catch (error) {
    console.error("Error assigning chat:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to assign chat"
    };
  }
}

/**
 * Update chat status
 */
export async function updateChatStatus(chatId: number, status: "active" | "resolved" | "closed") {
  try {
    await db
      .update(supportChats)
      .set({ 
        status,
        updatedAt: new Date().toISOString()
      })
      .where(eq(supportChats.id, chatId));
    
    return { success: true };
  } catch (error) {
    console.error("Error updating chat status:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to update chat status"
    };
  }
}

/**
 * Mark messages as read for admin
 */
export async function markAdminMessagesAsRead(chatId: number) {
  try {
    const adminPartyId = await getCurrentAdminPartyId();
    
    await db
      .update(supportChatMessages)
      .set({ 
        isRead: true,
        readAt: new Date().toISOString()
      })
      .where(
        and(
          eq(supportChatMessages.chatId, chatId),
          eq(supportChatMessages.isRead, false),
          // Only mark messages from drivers as read (not other admins)
          sql`${supportChatMessages.senderPartyId} != ${adminPartyId}`
        )
      );
    
    return { success: true };
  } catch (error) {
    console.error("Error marking messages as read:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to mark messages as read"
    };
  }
}

/**
 * Get customer details for admin view
 */
export async function getCustomerDetails(driverPartyId: number) {
  try {
    // Get basic customer info
    const customerInfo = await db
      .select({
        party: party,
        individual: individual,
      })
      .from(party)
      .leftJoin(individual, eq(party.id, individual.partyId))
      .where(eq(party.id, driverPartyId))
      .limit(1);
    
    if (customerInfo.length === 0) {
      throw new Error("Customer not found");
    }
    
    // Get additional contact points (phone, address)
    const contacts = await db
      .select()
      .from(contactPoint)
      .where(eq(contactPoint.partyId, driverPartyId));
    
    const customer = customerInfo[0];
    const email = contacts.find(c => c.contactType === "email")?.value;
    const phone = contacts.find(c => c.contactType === "phone")?.value;
    const address = contacts.find(c => c.contactType === "address")?.value;
    
    return {
      success: true,
      customer: {
        id: customer.party.id,
        firstName: customer.individual?.firstName || "",
        lastName: customer.individual?.lastName || "",
        email: email || "",
        phone: phone || "",
        address: address || "",
        joinDate: customer.party.createdAt || "",
        status: "active" as const, // You might want to determine this based on business logic
        kycStatus: "verified" as const, // You might want to determine this based on verification records
      }
    };
  } catch (error) {
    console.error("Error getting customer details:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get customer details",
      customer: null
    };
  }
}