"use server";

import { db } from "@/db";
import { eq, and, desc, sql, inArray } from "drizzle-orm";
import { getCurrentUserNoCache } from "@/lib/amplifyServerUtils";
import {
  vehicles,
  vehicleModel,
  vehicleMake,
  vehicleMedia,
  vehicleDocuments,
  party,
  inventory,
  individual,
} from "@/drizzle/schema";
import type { InventoryVehicle } from "@/app/(admin)/admin/types/inventory";

// ✅ Fixed: Use correct InventoryVehicle properties
export async function createInventoryVehicle(
  vehicleData: InventoryVehicle
): Promise<{
  success: boolean;
  data?: any;
  error?: string;
}> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      throw new Error("Admin party ID not found");
    }

    // Validate required fields
    if (!vehicleData.vinNumber) {
      return { success: false, error: "VIN number is required" };
    }

    // Check if VIN already exists
    const existingVehicle = await db
      .select({ id: vehicles.id })
      .from(vehicles)
      .where(eq(vehicles.vinNumber, vehicleData.vinNumber))
      .limit(1);

    if (existingVehicle.length > 0) {
      return {
        success: false,
        error: "A vehicle with this VIN number already exists",
      };
    }

    // Find the model ID based on make/model names
    let modelId: number | null = null;

    if (vehicleData.make && vehicleData.model) {
      const modelResult = await db
        .select({ id: vehicleModel.id })
        .from(vehicleModel)
        .leftJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
        .where(
          and(
            eq(vehicleMake.name, vehicleData.make),
            eq(vehicleModel.model, vehicleData.model)
          )
        )
        .limit(1);

      if (modelResult.length > 0) {
        modelId = modelResult[0].id;
      }
    }

    if (!modelId) {
      return {
        success: false,
        error: `Vehicle model not found for ${vehicleData.make} ${vehicleData.model}`,
      };
    }

    // Create vehicle AND add to inventory in single transaction
    const result = await db.transaction(async (tx) => {
      // 1. Create the actual vehicle record
      const newVehicle = await tx
        .insert(vehicles)
        .values({
          partyId: adminPartyId,
          modelId: modelId,
          vinNumber: vehicleData.vinNumber,
          vehicleRegistration: vehicleData.registrationNumber || null,
          countryId: 1,
          manufacturingYear: vehicleData.year || null,
          purchaseDate: new Date().toISOString(),
          color: vehicleData.color || null,
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        })
        .returning();

      const vehicleId = newVehicle[0].id;

      // 2. Add vehicle images if provided
      if (vehicleData.images && vehicleData.images.length > 0) {
        const mediaInserts = vehicleData.images.map((image) => ({
          vehicleId: vehicleId,
          mediaPath: image.imageUrl,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }));

        await tx.insert(vehicleMedia).values(mediaInserts);
        console.log(
          `✅ Inserted ${mediaInserts.length} vehicle media records for inventory vehicle ${vehicleId}`
        );
      }

      // 2.5. ✅ ADD: Save vehicle documents if provided
      if (vehicleData.vehicleDocuments && vehicleData.vehicleDocuments.length > 0) {
        // Map form document types to database document types
        // Database only supports: "registration", "insurance", "inspection", "other"
        const mapFormTypeToDbType = (formType: string) => {
          switch (formType) {
            case "registration": return "registration";
            case "license": return "other"; // Vehicle license document -> other
            case "dekra_inspection": return "inspection";
            case "insurance": return "insurance";
            case "service_history": return "other";
            case "operator_license": return "other";
            case "other": return "other";
            default: return "other";
          }
        };

        const documentInserts = vehicleData.vehicleDocuments.map((doc) => ({
          vehicleId: vehicleId,
          mediaPath: doc.filePath,
          documentType: mapFormTypeToDbType(doc.type) as "registration" | "insurance" | "inspection" | "other",
          expirationDate: doc.expiryDate ? new Date(doc.expiryDate).toISOString() : null,
          name: `${doc.type}:${doc.name}`, // Store original type with name for retrieval
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }));

        await tx.insert(vehicleDocuments).values(documentInserts);
        console.log(
          `✅ Inserted ${documentInserts.length} vehicle document records for inventory vehicle ${vehicleId}`
        );
      }

      // 3. ✅ CREATE: inventory record with catalogId and additional data
      const additionalData = {
        mileage: vehicleData.mileage || 0,
        condition: vehicleData.condition || "used",
        userNotes: vehicleData.notes || "",
      };

      const inventoryRecord = await tx
        .insert(inventory)
        .values({
          vehicleId,
          adminPartyId,
          catalogId: vehicleData.catalogId, // ✅ Store the catalog reference
          status: vehicleData.status || "available", // ✅ Use form status
          location: vehicleData.location || null,
          notes: JSON.stringify(additionalData), // ✅ Store mileage, condition, and notes as JSON
          effectiveFrom: new Date().toISOString(),
          // effectiveTo defaults to infinity
        })
        .returning();

      return {
        vehicleId,
        vehicle: newVehicle[0],
        inventoryRecord: inventoryRecord[0],
      };
    });

    console.log(
      `✅ Created vehicle ${result.vehicleId} and added to inventory`
    );

    return {
      success: true,
      data: result,
    };
  } catch (error) {
    console.error("Error creating inventory vehicle:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to create inventory vehicle",
    };
  }
}

// ✅ Fixed: Update inventory vehicle using correct properties
export async function updateInventoryVehicle(
  vehicleId: number,
  vehicleData: InventoryVehicle
): Promise<{
  success: boolean;
  data?: any;
  error?: string;
}> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      throw new Error("Admin party ID not found");
    }

    // Verify vehicle exists and belongs to admin
    const existingVehicle = await db
      .select({
        id: vehicles.id,
        partyId: vehicles.partyId,
      })
      .from(vehicles)
      .where(eq(vehicles.id, vehicleId))
      .limit(1);

    if (existingVehicle.length === 0) {
      return { success: false, error: "Vehicle not found" };
    }

    if (existingVehicle[0].partyId !== adminPartyId) {
      return { success: false, error: "Not authorized to update this vehicle" };
    }

    // Find the model ID if make/model provided
    let modelId: number | undefined = undefined;

    if (vehicleData.make && vehicleData.model) {
      const modelResult = await db
        .select({ id: vehicleModel.id })
        .from(vehicleModel)
        .leftJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
        .where(
          and(
            eq(vehicleMake.name, vehicleData.make),
            eq(vehicleModel.model, vehicleData.model)
          )
        )
        .limit(1);

      if (modelResult.length > 0) {
        modelId = modelResult[0].id;
      }
    }

    // Update vehicle in transaction
    const result = await db.transaction(async (tx) => {
      const updateData: any = {
        updatedAt: new Date().toISOString(),
      };

      // Only update fields that are provided
      if (modelId) updateData.modelId = modelId;
      if (vehicleData.vinNumber) updateData.vinNumber = vehicleData.vinNumber;
      if (vehicleData.registrationNumber)
        updateData.vehicleRegistration = vehicleData.registrationNumber;
      if (vehicleData.year) updateData.manufacturingYear = vehicleData.year;
      if (vehicleData.color) updateData.color = vehicleData.color;

      const updatedVehicle = await tx
        .update(vehicles)
        .set(updateData)
        .where(eq(vehicles.id, vehicleId))
        .returning();

      // Update vehicle images if provided
      if (vehicleData.images !== undefined) {
        console.log(
          "🔄 Updating images for inventory vehicle:",
          vehicleId,
          "New images:",
          vehicleData.images
        );

        // Delete existing images
        await tx
          .delete(vehicleMedia)
          .where(eq(vehicleMedia.vehicleId, vehicleId));
        console.log("🗑️  Deleted existing images for vehicle:", vehicleId);

        // Insert new images
        if (vehicleData.images.length > 0) {
          const mediaInserts = vehicleData.images.map((image) => ({
            vehicleId: vehicleId,
            mediaPath: image.imageUrl,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          }));

          await tx.insert(vehicleMedia).values(mediaInserts);
          console.log(
            `✅ Inserted ${mediaInserts.length} updated vehicle media records for inventory vehicle ${vehicleId}`
          );
        } else {
          console.log("ℹ️  No new images to insert for vehicle:", vehicleId);
        }
      }

      // ✅ ADD: Handle vehicle documents update
      if (vehicleData.vehicleDocuments) {
        // Delete existing documents for this vehicle
        await tx
          .delete(vehicleDocuments)
          .where(eq(vehicleDocuments.vehicleId, vehicleId));

        // Insert new documents
        if (vehicleData.vehicleDocuments.length > 0) {
          // Map form document types to database document types
          // Database only supports: "registration", "insurance", "inspection", "other"
          const mapFormTypeToDbType = (formType: string) => {
            switch (formType) {
              case "registration": return "registration";
              case "license": return "other"; // Vehicle license document -> other
              case "dekra_inspection": return "inspection";
              case "insurance": return "insurance";
              case "service_history": return "other";
              case "operator_license": return "other";
              case "other": return "other";
              default: return "other";
            }
          };

          const documentInserts = vehicleData.vehicleDocuments.map((doc) => ({
            vehicleId: vehicleId,
            mediaPath: doc.filePath,
            documentType: mapFormTypeToDbType(doc.type) as "registration" | "insurance" | "inspection" | "other",
            expirationDate: doc.expiryDate ? new Date(doc.expiryDate).toISOString() : null,
            name: `${doc.type}:${doc.name}`, // Store original type with name for retrieval
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          }));

          await tx.insert(vehicleDocuments).values(documentInserts);
          console.log(
            `✅ Inserted ${documentInserts.length} updated vehicle document records for inventory vehicle ${vehicleId}`
          );
        } else {
          console.log("ℹ️  No new documents to insert for vehicle:", vehicleId);
        }
      }

      // ✅ ADD: Update inventory record with status, location, and additional data
      const additionalData = {
        mileage: vehicleData.mileage || 0,
        condition: vehicleData.condition || "used",
        userNotes: vehicleData.notes || "",
      };

      await tx
        .update(inventory)
        .set({
          status: vehicleData.status || "available",
          location: vehicleData.location || null,
          notes: JSON.stringify(additionalData),
          updatedAt: new Date().toISOString(),
        })
        .where(
          and(
            eq(inventory.vehicleId, vehicleId),
            eq(inventory.effectiveTo, sql`'infinity'::timestamp`)
          )
        );

      console.log(`✅ Updated inventory record for vehicle ${vehicleId}`);

      return {
        vehicleId,
        vehicle: updatedVehicle[0],
      };
    });

    console.log(`✅ Updated inventory vehicle ${vehicleId}`);

    return {
      success: true,
      data: result,
    };
  } catch (error) {
    console.error("Error updating inventory vehicle:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to update inventory vehicle",
    };
  }
}

// Add vehicle to inventory (creates initial record)
export async function addVehicleToInventory(
  vehicleId: number,
  location?: string,
  notes?: string
): Promise<{ success: boolean; data?: any; error?: string }> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      throw new Error("Admin party ID not found");
    }

    // Check if vehicle is already in active inventory
    const existingInventory = await db
      .select({ id: inventory.id })
      .from(inventory)
      .where(
        and(
          eq(inventory.vehicleId, vehicleId),
          sql`${inventory.effectiveTo} = 'infinity'::timestamp`
        )
      )
      .limit(1);

    if (existingInventory.length > 0) {
      return { success: false, error: "Vehicle is already in inventory" };
    }

    const result = await db
      .insert(inventory)
      .values({
        vehicleId,
        adminPartyId,
        status: "available",
        location: location || null,
        notes: notes || null,
        effectiveFrom: new Date().toISOString(),
        // effectiveTo defaults to infinity
      })
      .returning();

    return { success: true, data: result[0] };
  } catch (error) {
    console.error("Error adding vehicle to inventory:", error);
    return { success: false, error: "Failed to add vehicle to inventory" };
  }
}

// Update vehicle status (creates new immutable record)
export async function updateVehicleInventoryStatus(
  vehicleId: number,
  newStatus: "available" | "assigned" | "maintenance" | "inspection",
  assignedToPartyId?: number,
  notes?: string
): Promise<{ success: boolean; data?: any; error?: string }> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      throw new Error("Admin party ID not found");
    }

    const now = new Date().toISOString();

    const result = await db.transaction(async (tx) => {
      // 1. Close current active record
      const currentRecord = await tx
        .update(inventory)
        .set({
          effectiveTo: now,
          updatedAt: now,
        })
        .where(
          and(
            eq(inventory.vehicleId, vehicleId),
            eq(inventory.adminPartyId, adminPartyId),
            sql`${inventory.effectiveTo} = 'infinity'::timestamp`
          )
        )
        .returning();

      if (currentRecord.length === 0) {
        throw new Error("Vehicle not found in active inventory");
      }

      // 2. Create new record with updated status
      const newRecord = await tx
        .insert(inventory)
        .values({
          vehicleId,
          adminPartyId,
          status: newStatus,
          assignedToPartyId: assignedToPartyId || null,
          location: currentRecord[0].location, // Inherit location
          notes: notes || `Status changed to ${newStatus}`,
          effectiveFrom: now,
          // effectiveTo defaults to infinity
        })
        .returning();

      return { currentRecord: currentRecord[0], newRecord: newRecord[0] };
    });

    console.log(`✅ Updated vehicle ${vehicleId} status to ${newStatus}`);
    return { success: true, data: result };
  } catch (error) {
    console.error("Error updating vehicle inventory status:", error);
    return { success: false, error: "Failed to update vehicle status" };
  }
}

// ✅ NEW: Get vehicle documents for editing
export async function getVehicleDocuments(
  vehicleId: number
): Promise<{
  success: boolean;
  data?: Array<{
    id: string;
    type: "registration" | "license" | "dekra_inspection" | "insurance" | "service_history" | "operator_license" | "other";
    name: string;
    filePath: string;
    uploadDate: string;
    expiryDate?: string;
    status: "valid" | "expired" | "expiring_soon";
  }>;
  error?: string;
}> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      throw new Error("Admin party ID not found");
    }

    // Get documents for this vehicle
    const documents = await db
      .select({
        id: vehicleDocuments.id,
        type: vehicleDocuments.documentType,
        name: vehicleDocuments.name,
        filePath: vehicleDocuments.mediaPath,
        uploadDate: vehicleDocuments.createdAt,
        expiryDate: vehicleDocuments.expirationDate,
      })
      .from(vehicleDocuments)
      .where(eq(vehicleDocuments.vehicleId, vehicleId));

    // Transform to match the form interface
    const transformedDocuments = documents.map((doc) => {
      const getDocumentStatus = (expiryDate?: string | null): "valid" | "expired" | "expiring_soon" => {
        if (!expiryDate) return "valid";
        const expiry = new Date(expiryDate);
        const now = new Date();
        const thirtyDaysFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);

        if (expiry < now) return "expired";
        if (expiry < thirtyDaysFromNow) return "expiring_soon";
        return "valid";
      };

      // Extract original type from name (format: "originalType:displayName")
      const extractOriginalType = (name: string | null, dbType: string) => {
        if (name && name.includes(':')) {
          const [originalType] = name.split(':');
          return originalType;
        }
        // Fallback to database type mapping for older records
        switch (dbType) {
          case "registration": return "registration";
          case "insurance": return "insurance";
          case "inspection": return "dekra_inspection";
          case "other": return "other";
          default: return "other";
        }
      };

      const extractDisplayName = (name: string | null) => {
        if (name && name.includes(':')) {
          const [, displayName] = name.split(':', 2);
          return displayName;
        }
        return name || "Document";
      };

      return {
        id: doc.id.toString(),
        type: extractOriginalType(doc.name, doc.type) as "registration" | "license" | "dekra_inspection" | "insurance" | "service_history" | "operator_license" | "other",
        name: extractDisplayName(doc.name),
        filePath: doc.filePath,
        uploadDate: doc.uploadDate || new Date().toISOString(),
        expiryDate: doc.expiryDate || undefined,
        status: getDocumentStatus(doc.expiryDate),
      };
    });

    return {
      success: true,
      data: transformedDocuments,
    };
  } catch (error) {
    console.error("Error getting vehicle documents:", error);
    return {
      success: false,
      error: "Failed to get vehicle documents",
    };
  }
}

// Get inventory vehicles (admin-owned vehicles)
export async function getInventoryVehicles(): Promise<{
  success: boolean;
  data?: any[];
  error?: string;
}> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      throw new Error("Admin party ID not found");
    }

    // ✅ FIXED: Query from inventory table and join with vehicles and assignments
    const inventoryVehicles = await db
      .select({
        id: vehicles.id,
        catalogId: inventory.catalogId, // ✅ Get catalogId from inventory table
        vinNumber: vehicles.vinNumber,
        vehicleRegistration: vehicles.vehicleRegistration,
        manufacturingYear: vehicles.manufacturingYear,
        color: vehicles.color,
        isActive: vehicles.isActive,
        createdAt: vehicles.createdAt,
        status: inventory.status, // ✅ Get status from inventory
        location: inventory.location, // ✅ Get location from inventory
        assignedToPartyId: inventory.assignedToPartyId, // ✅ Get assigned party ID
        effectiveFrom: inventory.effectiveFrom, // ✅ Get assignment date
        inventoryNotes: inventory.notes, // ✅ Get notes (contains JSON data)
        // Vehicle model info
        makeId: vehicleMake.id,
        make: vehicleMake.name,
        modelId: vehicleModel.id,
        model: vehicleModel.model,
        // Assigned driver info
        assignedDriverFirstName: individual.firstName,
        assignedDriverLastName: individual.lastName,
        assignedDriverEmail: sql<string>`(
          SELECT cp.value
          FROM contact_point cp
          WHERE cp.party_id = ${inventory.assignedToPartyId}
          AND cp.contact_point_type_id = 1
          AND cp.is_primary = true
          LIMIT 1
        )`,
      })
      .from(inventory) // ✅ Start from inventory table
      .leftJoin(vehicles, eq(inventory.vehicleId, vehicles.id))
      .leftJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
      .leftJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
      .leftJoin(party, eq(inventory.assignedToPartyId, party.id))
      .leftJoin(individual, eq(party.id, individual.partyId))
      .where(
        and(
          eq(inventory.adminPartyId, adminPartyId),
          sql`${inventory.effectiveTo} = 'infinity'::timestamp` // Only active records
        )
      )
      .orderBy(desc(inventory.effectiveFrom));

    // ✅ FIXED: Load documents and images for each vehicle
    const transformedVehicles = await Promise.all(
      inventoryVehicles.map(async (vehicle) => {
        const vehicleId = vehicle.id;

        // Load documents for this vehicle
        let vehicleDocuments: any[] = [];
        if (vehicleId) {
          const documentsResult = await getVehicleDocuments(vehicleId);
          if (documentsResult.success && documentsResult.data) {
            vehicleDocuments = documentsResult.data;
            console.log(`✅ Loaded ${vehicleDocuments.length} documents for vehicle ${vehicleId}`);
          }
        }

        // Load images for this vehicle
        let images: any[] = [];
        if (vehicleId) {
          const vehicleImages = await db
            .select({
              id: vehicleMedia.id,
              imageUrl: vehicleMedia.mediaPath,
              isPrimary: sql<boolean>`false`, // Default to false, could be enhanced later
            })
            .from(vehicleMedia)
            .where(eq(vehicleMedia.vehicleId, vehicleId));

          images = vehicleImages.map((img, index) => ({
            id: img.id,
            imageUrl: img.imageUrl,
            isPrimary: index === 0, // First image is primary by default
          }));

          console.log(`✅ Loaded ${images.length} images for vehicle ${vehicleId}`);
        }

        // Build assigned driver info if available
        const assignedDriver = vehicle.assignedToPartyId && vehicle.assignedDriverFirstName ? {
          id: vehicle.assignedToPartyId.toString(),
          name: `${vehicle.assignedDriverFirstName} ${vehicle.assignedDriverLastName || ""}`.trim(),
          email: vehicle.assignedDriverEmail || "",
          assignedDate: vehicle.effectiveFrom || new Date().toISOString(),
        } : undefined;

        // Parse additional data from notes JSON
        let additionalData = {
          mileage: 0,
          condition: "used" as const,
          userNotes: "",
        };

        try {
          if (vehicle.inventoryNotes) {
            const parsed = JSON.parse(vehicle.inventoryNotes);
            additionalData = {
              mileage: parsed.mileage || 0,
              condition: parsed.condition || "used",
              userNotes: parsed.userNotes || "",
            };
          }
        } catch (error) {
          // If JSON parsing fails, treat notes as plain text
          additionalData.userNotes = vehicle.inventoryNotes || "";
        }

        return {
          id: vehicle.id?.toString() || "",
          catalogId: vehicle.catalogId || "", // ✅ Now this exists!
          make: vehicle.make || "Unknown",
          model: vehicle.model || "Unknown",
          year: vehicle.manufacturingYear || 0,
          color: vehicle.color || "Unknown",
          vinNumber: vehicle.vinNumber || "Unknown",
          registrationNumber: vehicle.vehicleRegistration || "Unknown",
          mileage: additionalData.mileage, // ✅ Now from parsed JSON
          condition: additionalData.condition, // ✅ Now from parsed JSON
          status: vehicle.status || "available", // ✅ Now from database
          location: vehicle.location || "Unknown",
          assignedDriver, // ✅ Now includes assigned driver info
          vehicleDocuments, // ✅ Now includes actual documents
          images, // ✅ Now includes actual images
          lastInspection: undefined,
          nextService: undefined,
          createdAt: vehicle.createdAt || new Date().toISOString(),
          notes: additionalData.userNotes, // ✅ Now from parsed JSON
        };
      })
    );

    console.log(`🚗 [getInventoryVehicles] Loaded ${transformedVehicles.length} vehicles with documents and images`);

    return {
      success: true,
      data: transformedVehicles,
    };
  } catch (error) {
    console.error("Error fetching inventory vehicles:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to fetch inventory vehicles",
    };
  }
}

// Get current inventory (only active records)
export async function getCurrentInventoryVehicles(): Promise<{
  success: boolean;
  data?: any[];
  error?: string;
}> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      throw new Error("Admin party ID not found");
    }

    const inventoryVehicles = await db
      .select({
        inventoryId: inventory.id,
        vehicleId: inventory.vehicleId,
        status: inventory.status,
        assignedToPartyId: inventory.assignedToPartyId,
        location: inventory.location,
        notes: inventory.notes,
        effectiveFrom: inventory.effectiveFrom,
        // Vehicle details
        vinNumber: vehicles.vinNumber,
        vehicleRegistration: vehicles.vehicleRegistration,
        manufacturingYear: vehicles.manufacturingYear,
        color: vehicles.color,
        // Make/Model info
        make: vehicleMake.name,
        model: vehicleModel.model,
        // Assigned driver info
        assignedDriverName: sql<string>`CONCAT(${individual.firstName}, ' ', ${individual.lastName})`,
      })
      .from(inventory)
      .leftJoin(vehicles, eq(inventory.vehicleId, vehicles.id))
      .leftJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
      .leftJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
      .leftJoin(party, eq(inventory.assignedToPartyId, party.id))
      .leftJoin(individual, eq(party.id, individual.partyId))
      .where(
        and(
          eq(inventory.adminPartyId, adminPartyId),
          sql`${inventory.effectiveTo} = 'infinity'::timestamp` // Only current records
        )
      )
      .orderBy(desc(inventory.effectiveFrom));

    return { success: true, data: inventoryVehicles };
  } catch (error) {
    console.error("Error fetching current inventory:", error);
    return { success: false, error: "Failed to fetch inventory" };
  }
}

// Get available vehicles for assignment
export async function getAvailableVehiclesInventory(): Promise<{
  success: boolean;
  data?: any[];
  error?: string;
}> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      throw new Error("Admin party ID not found");
    }

    const availableVehicles = await db
      .select({
        vehicleId: inventory.vehicleId,
        inventoryId: inventory.id,
        catalogId: inventory.catalogId, // ✅ Include catalogId
        make: vehicleMake.name,
        model: vehicleModel.model,
        year: vehicles.manufacturingYear,
        color: vehicles.color,
        vinNumber: vehicles.vinNumber,
        vehicleRegistration: vehicles.vehicleRegistration,
        location: inventory.location,
      })
      .from(inventory)
      .leftJoin(vehicles, eq(inventory.vehicleId, vehicles.id))
      .leftJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
      .leftJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
      .where(
        and(
          eq(inventory.adminPartyId, adminPartyId),
          eq(inventory.status, "available"),
          sql`${inventory.effectiveTo} = 'infinity'::timestamp`
        )
      )
      .orderBy(desc(inventory.effectiveFrom));

    return { success: true, data: availableVehicles };
  } catch (error) {
    console.error("Error fetching available vehicles:", error);
    return { success: false, error: "Failed to fetch available vehicles" };
  }
}

// Get inventory stats
export async function getInventoryStats(): Promise<{
  success: boolean;
  data?: {
    total: number;
    available: number;
    assigned: number;
    maintenance: number;
  };
  error?: string;
}> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      throw new Error("Admin party ID not found");
    }

    const stats = await db
      .select({
        total: sql<number>`COUNT(*)`,
        available: sql<number>`COUNT(CASE WHEN status = 'available' THEN 1 END)`,
        assigned: sql<number>`COUNT(CASE WHEN status = 'assigned' THEN 1 END)`,
        maintenance: sql<number>`COUNT(CASE WHEN status = 'maintenance' THEN 1 END)`,
      })
      .from(inventory)
      .where(
        and(
          eq(inventory.adminPartyId, adminPartyId),
          sql`${inventory.effectiveTo} = 'infinity'::timestamp`
        )
      );

    return { success: true, data: stats[0] };
  } catch (error) {
    console.error("Error fetching inventory stats:", error);
    return { success: false, error: "Failed to fetch inventory stats" };
  }
}

// Mark vehicle as assigned
export async function markVehicleAsAssigned(
  vehicleId: number,
  assignedToPartyId: number,
  notes?: string
): Promise<{ success: boolean; data?: any; error?: string }> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      throw new Error("Admin party ID not found");
    }

    const now = new Date().toISOString();

    const result = await db.transaction(async (tx) => {
      // 1. Close current active record
      const currentRecord = await tx
        .update(inventory)
        .set({
          effectiveTo: now,
          updatedAt: now,
        })
        .where(
          and(
            eq(inventory.vehicleId, vehicleId),
            eq(inventory.adminPartyId, adminPartyId),
            sql`${inventory.effectiveTo} = 'infinity'::timestamp`
          )
        )
        .returning();

      if (currentRecord.length === 0) {
        throw new Error("Vehicle not found in active inventory");
      }

      // 2. Create new record with assigned status
      const newRecord = await tx
        .insert(inventory)
        .values({
          vehicleId,
          adminPartyId,
          status: "assigned",
          assignedToPartyId: assignedToPartyId,
          location: currentRecord[0].location, // Inherit location
          notes: notes || `Vehicle assigned to party ${assignedToPartyId}`,
          effectiveFrom: now,
          // effectiveTo defaults to infinity
        })
        .returning();

      return { currentRecord: currentRecord[0], newRecord: newRecord[0] };
    });

    console.log(
      `✅ Marked vehicle ${vehicleId} as assigned to party ${assignedToPartyId}`
    );
    return { success: true, data: result };
  } catch (error) {
    console.error("Error marking vehicle as assigned:", error);
    return { success: false, error: "Failed to mark vehicle as assigned" };
  }
}

// Mark vehicle as available again
export async function markVehicleAsAvailable(
  vehicleId: number
): Promise<{ success: boolean; data?: any; error?: string }> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      throw new Error("Admin party ID not found");
    }

    const now = new Date().toISOString();

    const result = await db.transaction(async (tx) => {
      // 1. Close current active record
      const currentRecord = await tx
        .update(inventory)
        .set({
          effectiveTo: now,
          updatedAt: now,
        })
        .where(
          and(
            eq(inventory.vehicleId, vehicleId),
            eq(inventory.adminPartyId, adminPartyId),
            sql`${inventory.effectiveTo} = 'infinity'::timestamp`
          )
        )
        .returning();

      if (currentRecord.length === 0) {
        throw new Error("Vehicle not found in active inventory");
      }

      // 2. Create new record with available status
      const newRecord = await tx
        .insert(inventory)
        .values({
          vehicleId,
          adminPartyId,
          status: "available",
          assignedToPartyId: null,
          location: currentRecord[0].location, // Inherit location
          notes: `Vehicle returned to inventory`,
          effectiveFrom: now,
          // effectiveTo defaults to infinity
        })
        .returning();

      return { currentRecord: currentRecord[0], newRecord: newRecord[0] };
    });

    console.log(`✅ Marked vehicle ${vehicleId} as available`);
    return { success: true, data: result };
  } catch (error) {
    console.error("Error marking vehicle as available:", error);
    return { success: false, error: "Failed to mark vehicle as available" };
  }
}
