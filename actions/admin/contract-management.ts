"use server";

import { db } from "@/db";
import { eq, and, isNull } from "drizzle-orm";
import { getCurrentUserNoCache } from "@/lib/amplifyServerUtils";
import { h_assignment_contracts } from "@/drizzle/h_schema/payment-contract";

/**
 * Server-side version of generateDocumentUrl that works in server actions
 */
async function generateDocumentUrlServer(path: string | undefined | null): Promise<string> {
  if (!path) return "";

  console.log("🔍 [generateDocumentUrlServer] Input path:", path);

  const result = await runWithAmplifyServerContext({
    nextServerContext: { cookies },
    operation: async (contextSpec) => {
      try {
        // Try with poolly bucket first (since uploads use this bucket)
        try {
          const getUrlResult = await getUrl(contextSpec, {
            path,
            options: {
              bucket: "poolly",
              validateObjectExistence: false,
              expiresIn: 20,
              useAccelerateEndpoint: false,
            },
          });
          console.log("✅ [generateDocumentUrlServer] Success with poolly bucket:", getUrlResult.url);
          return String(getUrlResult.url);
        } catch (poollyError) {
          console.log("🔍 [generateDocumentUrlServer] Failed with poolly bucket, trying default:", poollyError);

          // Fallback to default bucket
          const getUrlResult = await getUrl(contextSpec, {
            path,
            options: {
              validateObjectExistence: false,
              expiresIn: 20,
              useAccelerateEndpoint: false,
            },
          });
          console.log("✅ [generateDocumentUrlServer] Success with default bucket:", getUrlResult.url);
          return String(getUrlResult.url);
        }
      } catch (error) {
        console.error("❌ [generateDocumentUrlServer] Error generating document URL:", error);
        throw error;
      }
    },
  });

  return result;
}

/**
 * Debug function to check what contracts exist for an assignment
 */
export async function debugAssignmentContracts(assignmentId: number): Promise<{
  success: boolean;
  data?: any[];
  error?: string;
}> {
  try {
    console.log("🔍 [debugAssignmentContracts] Checking all contracts for assignment:", assignmentId);

    const contracts = await db
      .select()
      .from(h_assignment_contracts)
      .where(eq(h_assignment_contracts.assignmentId, assignmentId));

    console.log("🔍 [debugAssignmentContracts] All contracts found:", contracts);
    console.log("🔍 [debugAssignmentContracts] Contract count:", contracts.length);

    contracts.forEach((contract, index) => {
      console.log(`🔍 [debugAssignmentContracts] Contract ${index + 1}:`, {
        id: contract.id,
        assignmentId: contract.assignmentId,
        contractFilePath: contract.contractFilePath,
        originalFilename: contract.originalFilename,
        replacedBy: contract.replacedBy,
        uploadedAt: contract.uploadedAt,
      });
    });

    return {
      success: true,
      data: contracts,
    };
  } catch (error) {
    console.error("❌ [debugAssignmentContracts] Error:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

/**
 * Replace an existing contract with a new version (immutable update)
 * This maintains the audit trail by linking old and new contracts
 */
export async function replaceAssignmentContract(
  assignmentId: number,
  newContractS3Path: string,
  newContractFileName: string,
  newContractFileSize: number,
  newContractFileType: string,
  replacementReason?: string
): Promise<{
  success: boolean;
  data?: { oldContractId: number; newContractId: number };
  error?: string;
}> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      throw new Error("Admin party ID not found");
    }

    console.log("🔄 Replacing contract for assignment:", assignmentId);

    const result = await db.transaction(async (tx) => {
      // Step 1: Find the current active contract
      const [currentContract] = await tx
        .select()
        .from(h_assignment_contracts)
        .where(
          and(
            eq(h_assignment_contracts.assignmentId, assignmentId),
            isNull(h_assignment_contracts.replacedBy) // Current active contract
          )
        )
        .limit(1);

      if (!currentContract) {
        throw new Error("No active contract found for this assignment");
      }

      console.log("📄 Found current contract ID:", currentContract.id);

      // Step 2: Create the new contract record
      const [newContract] = await tx
        .insert(h_assignment_contracts)
        .values({
          assignmentId,
          contractFilePath: newContractS3Path,
          originalFilename: newContractFileName,
          fileSize: newContractFileSize,
          mimeType: newContractFileType,
          uploadedBy: adminPartyId,
          notes: replacementReason || "Contract replaced with new version",
        })
        .returning();

      console.log("✅ Created new contract ID:", newContract.id);

      // Step 3: Update the old contract to point to the new one
      await tx
        .update(h_assignment_contracts)
        .set({
          replacedBy: newContract.id,
        })
        .where(eq(h_assignment_contracts.id, currentContract.id));

      console.log("🔗 Linked old contract to new contract");

      return {
        oldContractId: currentContract.id,
        newContractId: newContract.id,
      };
    });

    return {
      success: true,
      data: result,
    };
  } catch (error) {
    console.error("❌ Error replacing contract:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to replace contract",
    };
  }
}

/**
 * Get the current active contract for an assignment
 */
export async function getCurrentAssignmentContract(assignmentId: number): Promise<{
  success: boolean;
  data?: any;
  error?: string;
}> {
  try {
    console.log("🔍 [getCurrentAssignmentContract] Querying for assignment ID:", assignmentId);

    const [contract] = await db
      .select()
      .from(h_assignment_contracts)
      .where(
        and(
          eq(h_assignment_contracts.assignmentId, assignmentId),
          isNull(h_assignment_contracts.replacedBy) // Current active contract
        )
      )
      .limit(1);

    console.log("🔍 [getCurrentAssignmentContract] Query result:", contract);

    if (!contract) {
      console.log("❌ [getCurrentAssignmentContract] No contract found for assignment:", assignmentId);
      return {
        success: false,
        error: "No active contract found for this assignment",
      };
    }

    console.log("✅ [getCurrentAssignmentContract] Found contract:", {
      id: contract.id,
      assignmentId: contract.assignmentId,
      contractFilePath: contract.contractFilePath,
      originalFilename: contract.originalFilename,
    });

    return {
      success: true,
      data: contract,
    };
  } catch (error) {
    console.error("❌ [getCurrentAssignmentContract] Error fetching current contract:", error);
    console.error("❌ [getCurrentAssignmentContract] Error details:", {
      message: error instanceof Error ? error.message : "Unknown error",
      assignmentId,
    });
    return {
      success: false,
      error: `Failed to fetch contract: ${error instanceof Error ? error.message : "Unknown error"}`,
    };
  }
}

/**
 * Get contract history for an assignment (all versions)
 */
export async function getAssignmentContractHistory(assignmentId: number): Promise<{
  success: boolean;
  data?: any[];
  error?: string;
}> {
  try {
    const contracts = await db
      .select()
      .from(h_assignment_contracts)
      .where(eq(h_assignment_contracts.assignmentId, assignmentId))
      .orderBy(h_assignment_contracts.uploadedAt);

    return {
      success: true,
      data: contracts,
    };
  } catch (error) {
    console.error("❌ Error fetching contract history:", error);
    return {
      success: false,
      error: "Failed to fetch contract history",
    };
  }
}

/**
 * Get contract file path and filename for client-side URL generation
 */
export async function getContractFilePath(contractId: number): Promise<{
  success: boolean;
  data?: { filePath: string; filename: string };
  error?: string;
}> {
  try {
    console.log("🔍 [getContractFilePath] Getting contract by ID:", contractId);

    const [contract] = await db
      .select()
      .from(h_assignment_contracts)
      .where(eq(h_assignment_contracts.id, contractId))
      .limit(1);

    console.log("🔍 [getContractFilePath] Contract query result:", contract);

    if (!contract) {
      console.log("❌ [getContractFilePath] Contract not found for ID:", contractId);
      return {
        success: false,
        error: "Contract not found",
      };
    }

    console.log("✅ [getContractFilePath] Found contract file path:", contract.contractFilePath);

    return {
      success: true,
      data: {
        filePath: contract.contractFilePath,
        filename: contract.originalFilename,
      },
    };
  } catch (error) {
    console.error("❌ [getContractFilePath] Error getting contract file path:", error);
    return {
      success: false,
      error: `Failed to get contract file path: ${error instanceof Error ? error.message : "Unknown error"}`,
    };
  }
}

/**
 * Get the file path for the current active contract for an assignment
 */
export async function getAssignmentContractFilePath(assignmentId: number): Promise<{
  success: boolean;
  data?: { filePath: string; filename: string };
  error?: string;
}> {
  try {
    console.log("🔍 [getAssignmentContractFilePath] Starting for assignment ID:", assignmentId);

    // First get the current active contract
    const contractResult = await getCurrentAssignmentContract(assignmentId);
    console.log("🔍 [getAssignmentContractFilePath] Contract result:", contractResult);

    if (!contractResult.success || !contractResult.data) {
      console.log("❌ [getAssignmentContractFilePath] No contract found:", contractResult.error);
      return {
        success: false,
        error: contractResult.error || "No active contract found",
      };
    }

    console.log("🔍 [getAssignmentContractFilePath] Found contract ID:", contractResult.data.id);
    console.log("🔍 [getAssignmentContractFilePath] Contract file path:", contractResult.data.contractFilePath);

    return {
      success: true,
      data: {
        filePath: contractResult.data.contractFilePath,
        filename: contractResult.data.originalFilename,
      },
    };
  } catch (error) {
    console.error("❌ [getAssignmentContractFilePath] Exception caught:", error);
    console.error("❌ [getAssignmentContractFilePath] Error details:", {
      message: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : "No stack trace",
      assignmentId,
    });
    return {
      success: false,
      error: `Failed to get contract file path: ${error instanceof Error ? error.message : "Unknown error"}`,
    };
  }
}
