"use server";

import { revalidatePath } from "next/cache";
import {
  createAssignment,
  getAllAssignments,
  getAssignmentById,
  CreateAssignmentInput,
  AssignmentRecord,
} from "@/drizzle-actions/admin/assignments";

/**
 * Server action to create a new assignment
 */
export async function createAssignmentAction(input: CreateAssignmentInput): Promise<{
  success: boolean;
  data?: AssignmentRecord;
  error?: string;
}> {
  try {
    const result = await createAssignment(input);
    
    if (result.success) {
      // Revalidate relevant pages
      revalidatePath("/admin/assignments");
      revalidatePath("/admin/payments");
    }
    
    return result;
  } catch (error) {
    console.error("❌ [createAssignmentAction] Error:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to create assignment",
    };
  }
}

/**
 * Server action to get all assignments
 */
export async function getAllAssignmentsAction(): Promise<{
  success: boolean;
  data?: AssignmentRecord[];
  error?: string;
}> {
  try {
    const result = await getAllAssignments();
    return result;
  } catch (error) {
    console.error("❌ [getAllAssignmentsAction] Error:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get assignments",
    };
  }
}

/**
 * Server action to get assignment by ID
 */
export async function getAssignmentByIdAction(assignmentId: number): Promise<{
  success: boolean;
  data?: AssignmentRecord;
  error?: string;
}> {
  try {
    const result = await getAssignmentById(assignmentId);
    return result;
  } catch (error) {
    console.error("❌ [getAssignmentByIdAction] Error:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get assignment",
    };
  }
}

/**
 * Server action to migrate existing group assignments to h_assignments table
 * This is a one-time migration function
 */
export async function migrateGroupAssignmentsAction(): Promise<{
  success: boolean;
  migratedCount?: number;
  error?: string;
}> {
  try {
    // This would contain the migration logic to move existing group-based assignments
    // to the new h_assignments table. For now, we'll return a placeholder.
    
    console.log("🔄 Migration function called - implement migration logic here");
    
    return {
      success: true,
      migratedCount: 0,
    };
  } catch (error) {
    console.error("❌ [migrateGroupAssignmentsAction] Error:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to migrate assignments",
    };
  }
}
