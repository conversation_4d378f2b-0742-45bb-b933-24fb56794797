"use server";

import { revalidatePath } from "next/cache";
import {
  recordWeeklyEarnings,
  updateWeeklyEarnings,
  getWeeklyEarnings,
  getWeeklyEarningsSummary,
  deleteWeeklyEarnings,
  validateWeeklyEarnings,
} from "@/drizzle-actions/admin/weekly-earnings";
import {
  WeeklyEarningsFormSchema,
  DateRangeSchema,
} from "@/schemas/payment-contract";
import {
  WeeklyEarningsRecord,
  WeeklyEarningsInput,
  DebtRecord,
  PaymentError,
  WeeklyEarningsSummary,
} from "@/types/payment-contract";

// =====================================================
// WEEKLY EARNINGS RECORDING ACTIONS
// =====================================================

/**
 * Server action to record weekly earnings for an assignment
 */
export async function recordWeeklyEarningsAction(
  prevState: any,
  formData: FormData
): Promise<{
  success: boolean;
  data?: WeeklyEarningsRecord;
  debtCreated?: DebtRecord;
  error?: string;
  fieldErrors?: Record<string, string>;
}> {
  try {
    // Extract form data
    const assignmentId = Number(formData.get("assignmentId"));
    const weekStartDate = formData.get("weekStartDate") as string;
    const weekEndDate = formData.get("weekEndDate") as string;
    const totalEarnings = Number(formData.get("totalEarnings"));
    const fuelCosts = formData.get("fuelCosts")
      ? Number(formData.get("fuelCosts"))
      : undefined;
    const otherExpenses = formData.get("otherExpenses")
      ? Number(formData.get("otherExpenses"))
      : undefined;
    const notes = formData.get("notes") as string | null;

    // Extract platform earnings (JSON format)
    const platformEarningsStr = formData.get("platformEarnings") as string;
    let platformEarnings: Record<string, number> = {};

    if (platformEarningsStr) {
      try {
        platformEarnings = JSON.parse(platformEarningsStr);
      } catch (parseError) {
        return {
          success: false,
          error: "Invalid platform earnings format",
          fieldErrors: {
            platformEarnings: "Platform earnings must be valid JSON",
          },
        };
      }
    }

    // Basic validation
    if (!assignmentId || assignmentId <= 0) {
      return {
        success: false,
        error: "Invalid assignment ID",
        fieldErrors: {
          assignmentId: "Assignment ID is required",
        },
      };
    }

    if (!weekStartDate || !weekEndDate) {
      return {
        success: false,
        error: "Week dates are required",
        fieldErrors: {
          weekStartDate: !weekStartDate ? "Week start date is required" : "",
          weekEndDate: !weekEndDate ? "Week end date is required" : "",
        },
      };
    }

    if (isNaN(totalEarnings) || totalEarnings < 0) {
      return {
        success: false,
        error: "Invalid total earnings",
        fieldErrors: {
          totalEarnings: "Total earnings must be a non-negative number",
        },
      };
    }

    // Prepare input for validation (only fields that schema expects)
    const validationInput = {
      assignmentId,
      weekStartDate,
      grossEarnings: totalEarnings,
      platformName: "uber", // Default platform
      weeklyTarget: 2700,   // Default target
      notes: notes || undefined,
    };

    console.log("🔍 [recordWeeklyEarningsAction] Validation input:", validationInput);

    // Validate with Zod schema
    const validationResult = WeeklyEarningsFormSchema.safeParse(validationInput);
    if (!validationResult.success) {
      const fieldErrors: Record<string, string> = {};
      validationResult.error.errors.forEach((error) => {
        const field = error.path[0] as string;
        fieldErrors[field] = error.message;
      });

      console.log("❌ [recordWeeklyEarningsAction] Validation errors:", fieldErrors);
      console.log("❌ [recordWeeklyEarningsAction] Validation details:", validationResult.error.errors);

      return {
        success: false,
        error: "Please correct the form errors",
        fieldErrors,
      };
    }

    // Prepare full input for the recording function
    const fullInput: WeeklyEarningsInput = {
      assignmentId,
      weekStartDate,
      weekEndDate,
      grossEarnings: totalEarnings,
      totalEarnings,
      platformEarnings,
      fuelCosts,
      otherExpenses,
      platformName: "uber",
      weeklyTarget: 2700,
      notes: notes || undefined,
    };

    // Additional business validation
    const businessValidation = await validateWeeklyEarnings(fullInput);
    if (!businessValidation.isValid) {
      const fieldErrors: Record<string, string> = {};
      businessValidation.errors.forEach((error: PaymentError) => {
        if (error.field) {
          fieldErrors[error.field] = error.message;
        }
      });

      console.log("❌ [recordWeeklyEarningsAction] Business validation errors:", fieldErrors);

      return {
        success: false,
        error: "Validation failed",
        fieldErrors,
      };
    }

    // Record the earnings
    const result = await recordWeeklyEarnings(fullInput);

    if (!result.success) {
      return {
        success: false,
        error: result.error?.message || "Failed to record weekly earnings",
      };
    }

    // Revalidate relevant pages
    revalidatePath("/admin/payments");
    revalidatePath(`/admin/assignments/${assignmentId}`);

    return {
      success: true,
      data: result.data,
      debtCreated: result.debtCreated,
    };
  } catch (error) {
    console.error("❌ [recordWeeklyEarningsAction] Error:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to record weekly earnings",
    };
  }
}

/**
 * Server action to update existing weekly earnings
 */
export async function updateWeeklyEarningsAction(
  prevState: any,
  formData: FormData
): Promise<{
  success: boolean;
  data?: WeeklyEarningsRecord;
  error?: string;
  fieldErrors?: Record<string, string>;
}> {
  try {
    // Extract form data
    const earningsId = Number(formData.get("earningsId"));
    const totalEarnings = formData.get("totalEarnings")
      ? Number(formData.get("totalEarnings"))
      : undefined;
    const fuelCosts = formData.get("fuelCosts")
      ? Number(formData.get("fuelCosts"))
      : undefined;
    const otherExpenses = formData.get("otherExpenses")
      ? Number(formData.get("otherExpenses"))
      : undefined;
    const notes = formData.get("notes") as string | null;

    // Extract platform earnings (JSON format)
    const platformEarningsStr = formData.get("platformEarnings") as string;
    let platformEarnings: Record<string, number> | undefined;

    if (platformEarningsStr) {
      try {
        platformEarnings = JSON.parse(platformEarningsStr);
      } catch (parseError) {
        return {
          success: false,
          error: "Invalid platform earnings format",
          fieldErrors: {
            platformEarnings: "Platform earnings must be valid JSON",
          },
        };
      }
    }

    // Basic validation
    if (!earningsId || earningsId <= 0) {
      return {
        success: false,
        error: "Invalid earnings ID",
        fieldErrors: {
          earningsId: "Earnings ID is required",
        },
      };
    }

    // Validate numeric fields if provided
    if (
      totalEarnings !== undefined &&
      (isNaN(totalEarnings) || totalEarnings < 0)
    ) {
      return {
        success: false,
        error: "Invalid total earnings",
        fieldErrors: {
          totalEarnings: "Total earnings must be a non-negative number",
        },
      };
    }

    if (fuelCosts !== undefined && (isNaN(fuelCosts) || fuelCosts < 0)) {
      return {
        success: false,
        error: "Invalid fuel costs",
        fieldErrors: {
          fuelCosts: "Fuel costs must be a non-negative number",
        },
      };
    }

    if (
      otherExpenses !== undefined &&
      (isNaN(otherExpenses) || otherExpenses < 0)
    ) {
      return {
        success: false,
        error: "Invalid other expenses",
        fieldErrors: {
          otherExpenses: "Other expenses must be a non-negative number",
        },
      };
    }

    // Prepare updates
    const updates: Partial<WeeklyEarningsInput> = {};
    if (totalEarnings !== undefined) {
      updates.totalEarnings = totalEarnings;
      updates.grossEarnings = totalEarnings; // Use totalEarnings as grossEarnings for now
    }
    if (fuelCosts !== undefined) updates.fuelCosts = fuelCosts;
    if (otherExpenses !== undefined) updates.otherExpenses = otherExpenses;
    if (platformEarnings !== undefined)
      updates.platformEarnings = platformEarnings;
    if (notes !== null) updates.notes = notes || undefined;

    // Update the earnings
    const result = await updateWeeklyEarnings(earningsId, updates);

    if (!result.success) {
      return {
        success: false,
        error: result.error?.message || "Failed to update weekly earnings",
      };
    }

    // Revalidate relevant pages
    revalidatePath("/admin/payments");
    if (result.data) {
      revalidatePath(`/admin/assignments/${result.data.assignmentId}`);
    }

    return {
      success: true,
      data: result.data,
    };
  } catch (error) {
    console.error("❌ [updateWeeklyEarningsAction] Error:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to update weekly earnings",
    };
  }
}

// =====================================================
// WEEKLY EARNINGS RETRIEVAL ACTIONS
// =====================================================

/**
 * Server action to get weekly earnings for an assignment
 */
export async function getWeeklyEarningsAction(
  assignmentId: number,
  startDate?: string,
  endDate?: string
): Promise<{
  success: boolean;
  data?: WeeklyEarningsRecord[];
  error?: string;
}> {
  try {
    if (!assignmentId || assignmentId <= 0) {
      return {
        success: false,
        error: "Invalid assignment ID",
      };
    }

    // Validate date range if provided
    if ((startDate && !endDate) || (!startDate && endDate)) {
      return {
        success: false,
        error: "Both start and end dates must be provided if using date range",
      };
    }

    if (startDate && endDate) {
      const dateRangeValidation = DateRangeSchema.safeParse({
        startDate,
        endDate,
      });

      if (!dateRangeValidation.success) {
        return {
          success: false,
          error: "Invalid date range",
        };
      }
    }

    const result = await getWeeklyEarnings(assignmentId, startDate, endDate);

    if (!result.success) {
      return {
        success: false,
        error: result.error?.message || "Failed to fetch weekly earnings",
      };
    }

    return {
      success: true,
      data: result.data || [],
    };
  } catch (error) {
    console.error("❌ [getWeeklyEarningsAction] Error:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to fetch weekly earnings",
    };
  }
}

/**
 * Server action to get weekly earnings summary for multiple assignments
 */
export async function getWeeklyEarningsSummaryAction(
  assignmentIds: number[],
  startDate?: string,
  endDate?: string
): Promise<{
  success: boolean;
  data?: WeeklyEarningsSummary[];
  error?: string;
}> {
  try {
    if (!Array.isArray(assignmentIds) || assignmentIds.length === 0) {
      return {
        success: false,
        error: "Invalid assignment IDs array",
      };
    }

    // Validate all assignment IDs are positive numbers
    const invalidIds = assignmentIds.filter((id) => !id || id <= 0);
    if (invalidIds.length > 0) {
      return {
        success: false,
        error: "All assignment IDs must be positive numbers",
      };
    }

    // Validate date range if provided
    if ((startDate && !endDate) || (!startDate && endDate)) {
      return {
        success: false,
        error: "Both start and end dates must be provided if using date range",
      };
    }

    if (startDate && endDate) {
      const dateRangeValidation = DateRangeSchema.safeParse({
        startDate,
        endDate,
      });

      if (!dateRangeValidation.success) {
        return {
          success: false,
          error: "Invalid date range",
        };
      }
    }

    const result = await getWeeklyEarningsSummary(
      assignmentIds,
      startDate,
      endDate
    );

    if (!result.success) {
      return {
        success: false,
        error: result.error?.message || "Failed to fetch earnings summary",
      };
    }

    return {
      success: true,
      data: result.data || [],
    };
  } catch (error) {
    console.error("❌ [getWeeklyEarningsSummaryAction] Error:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to fetch earnings summary",
    };
  }
}

// =====================================================
// WEEKLY EARNINGS MANAGEMENT ACTIONS
// =====================================================

/**
 * Server action to delete weekly earnings record
 */
export async function deleteWeeklyEarningsAction(
  prevState: any,
  formData: FormData
): Promise<{
  success: boolean;
  error?: string;
  fieldErrors?: Record<string, string>;
}> {
  try {
    const earningsId = Number(formData.get("earningsId"));
    const reason = formData.get("reason") as string | null;

    if (!earningsId || earningsId <= 0) {
      return {
        success: false,
        error: "Invalid earnings ID",
        fieldErrors: {
          earningsId: "Earnings ID is required",
        },
      };
    }

    if (!reason?.trim()) {
      return {
        success: false,
        error: "Deletion reason is required",
        fieldErrors: {
          reason: "Please provide a reason for deleting this earnings record",
        },
      };
    }

    const result = await deleteWeeklyEarnings(earningsId, reason);

    if (!result.success) {
      return {
        success: false,
        error: result.error?.message || "Failed to delete earnings record",
      };
    }

    // Revalidate relevant pages
    revalidatePath("/admin/payments");

    return {
      success: true,
    };
  } catch (error) {
    console.error("❌ [deleteWeeklyEarningsAction] Error:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to delete weekly earnings record",
    };
  }
}

/**
 * Server action to refresh earnings data
 */
export async function refreshWeeklyEarningsAction(
  assignmentId?: number
): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    // Revalidate relevant pages
    revalidatePath("/admin/payments");

    if (assignmentId) {
      revalidatePath(`/admin/assignments/${assignmentId}`);
    }

    return {
      success: true,
    };
  } catch (error) {
    console.error("❌ [refreshWeeklyEarningsAction] Error:", error);
    return {
      success: false,
      error: "Failed to refresh earnings data",
    };
  }
}

// =====================================================
// BULK OPERATIONS ACTIONS
// =====================================================

/**
 * Server action to record earnings for multiple weeks (bulk operation)
 */
export async function recordBulkWeeklyEarningsAction(
  prevState: any,
  formData: FormData
): Promise<{
  success: boolean;
  data?: {
    successful: WeeklyEarningsRecord[];
    failed: Array<{ week: string; error: string }>;
    debtsCreated: DebtRecord[];
  };
  error?: string;
}> {
  try {
    const assignmentId = Number(formData.get("assignmentId"));
    const bulkDataStr = formData.get("bulkData") as string;

    if (!assignmentId || assignmentId <= 0) {
      return {
        success: false,
        error: "Invalid assignment ID",
      };
    }

    if (!bulkDataStr) {
      return {
        success: false,
        error: "Bulk data is required",
      };
    }

    let bulkData: WeeklyEarningsInput[];
    try {
      bulkData = JSON.parse(bulkDataStr);
    } catch (parseError) {
      return {
        success: false,
        error: "Invalid bulk data format",
      };
    }

    if (!Array.isArray(bulkData) || bulkData.length === 0) {
      return {
        success: false,
        error: "Bulk data must be a non-empty array",
      };
    }

    // Process each earnings record
    const results = {
      successful: [] as WeeklyEarningsRecord[],
      failed: [] as Array<{ week: string; error: string }>,
      debtsCreated: [] as DebtRecord[],
    };

    for (const earningsInput of bulkData) {
      try {
        // Ensure assignment ID matches
        earningsInput.assignmentId = assignmentId;

        const result = await recordWeeklyEarnings(earningsInput);

        if (result.success && result.data) {
          results.successful.push(result.data);
          if (result.debtCreated) {
            results.debtsCreated.push(result.debtCreated);
          }
        } else {
          results.failed.push({
            week: `${earningsInput.weekStartDate} - ${earningsInput.weekEndDate}`,
            error: result.error?.message || "Unknown error",
          });
        }
      } catch (recordError) {
        results.failed.push({
          week: `${earningsInput.weekStartDate} - ${earningsInput.weekEndDate}`,
          error:
            recordError instanceof Error
              ? recordError.message
              : "Processing error",
        });
      }
    }

    // Revalidate relevant pages
    revalidatePath("/admin/payments");
    revalidatePath(`/admin/assignments/${assignmentId}`);

    return {
      success: true,
      data: results,
    };
  } catch (error) {
    console.error("❌ [recordBulkWeeklyEarningsAction] Error:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to process bulk earnings",
    };
  }
}

// =====================================================
// UTILITY ACTIONS
// =====================================================

/**
 * Server action to validate weekly earnings form data
 */
export async function validateWeeklyEarningsFormAction(
  prevState: any,
  formData: FormData
): Promise<{
  success: boolean;
  fieldErrors?: Record<string, string>;
  error?: string;
}> {
  try {
    // Extract form data (similar to recordWeeklyEarningsAction but for validation only)
    const assignmentId = Number(formData.get("assignmentId"));
    const weekStartDate = formData.get("weekStartDate") as string;
    const weekEndDate = formData.get("weekEndDate") as string;
    const totalEarnings = Number(formData.get("totalEarnings"));
    const fuelCosts = formData.get("fuelCosts")
      ? Number(formData.get("fuelCosts"))
      : undefined;
    const otherExpenses = formData.get("otherExpenses")
      ? Number(formData.get("otherExpenses"))
      : undefined;
    const notes = formData.get("notes") as string | null;

    const input: WeeklyEarningsInput = {
      assignmentId,
      weekStartDate,
      weekEndDate,
      grossEarnings: totalEarnings, // Use totalEarnings as grossEarnings for now
      totalEarnings,
      fuelCosts,
      otherExpenses,
      notes: notes || undefined,
    };

    // Validate with Zod schema
    const validationResult = WeeklyEarningsFormSchema.safeParse(input);
    if (!validationResult.success) {
      const fieldErrors: Record<string, string> = {};
      validationResult.error.errors.forEach((error) => {
        const field = error.path[0] as string;
        fieldErrors[field] = error.message;
      });

      return {
        success: false,
        fieldErrors,
      };
    }

    // Business validation
    const businessValidation = validateWeeklyEarnings(input);
    if (!businessValidation.isValid) {
      const fieldErrors: Record<string, string> = {};
      businessValidation.errors.forEach((error: PaymentError) => {
        if (error.field) {
          fieldErrors[error.field] = error.message;
        }
      });

      return {
        success: false,
        fieldErrors,
      };
    }

    return {
      success: true,
    };
  } catch (error) {
    console.error("❌ [validateWeeklyEarningsFormAction] Error:", error);
    return {
      success: false,
      error: "Form validation failed",
    };
  }
}

/**
 * Get earnings statistics for an assignment (for dashboard widgets)
 */
export async function getEarningsStatsAction(
  assignmentId: number,
  period: "week" | "month" | "quarter" | "year" = "month"
): Promise<{
  success: boolean;
  data?: {
    totalEarnings: string;
    totalExpected: string;
    totalShortfall: string;
    averageWeekly: string;
    weeksRecorded: number;
    shortfallPercentage: number;
  };
  error?: string;
}> {
  try {
    if (!assignmentId || assignmentId <= 0) {
      return {
        success: false,
        error: "Invalid assignment ID",
      };
    }

    // Calculate date range based on period
    const now = new Date();
    let startDate: string;

    switch (period) {
      case "week":
        const weekStart = new Date(now);
        weekStart.setDate(now.getDate() - now.getDay()); // Start of current week
        startDate = weekStart.toISOString().split("T")[0];
        break;

      case "month":
        startDate = new Date(now.getFullYear(), now.getMonth(), 1)
          .toISOString()
          .split("T")[0];
        break;

      case "quarter":
        const quarterStart = new Date(
          now.getFullYear(),
          Math.floor(now.getMonth() / 3) * 3,
          1
        );
        startDate = quarterStart.toISOString().split("T")[0];
        break;

      case "year":
        startDate = new Date(now.getFullYear(), 0, 1)
          .toISOString()
          .split("T")[0];
        break;

      default:
        startDate = new Date(now.getFullYear(), now.getMonth(), 1)
          .toISOString()
          .split("T")[0];
    }

    const endDate = now.toISOString().split("T")[0];

    const summaryResult = await getWeeklyEarningsSummary(
      [assignmentId],
      startDate,
      endDate
    );

    if (
      !summaryResult.success ||
      !summaryResult.data ||
      summaryResult.data.length === 0
    ) {
      return {
        success: true,
        data: {
          totalEarnings: "0",
          totalExpected: "0",
          totalShortfall: "0",
          averageWeekly: "0",
          weeksRecorded: 0,
          shortfallPercentage: 0,
        },
      };
    }

    const summary = summaryResult.data[0];
    const totalEarnings = Number(summary.totalEarnings);
    const totalExpected = Number(summary.totalExpectedEarnings);
    const shortfallPercentage =
      totalExpected > 0
        ? (Number(summary.totalShortfall) / totalExpected) * 100
        : 0;

    return {
      success: true,
      data: {
        totalEarnings: summary.totalEarnings,
        totalExpected: summary.totalExpectedEarnings,
        totalShortfall: summary.totalShortfall,
        averageWeekly: summary.averageWeeklyEarnings,
        weeksRecorded: summary.weeksRecorded,
        shortfallPercentage: Math.round(shortfallPercentage * 100) / 100,
      },
    };
  } catch (error) {
    console.error("❌ [getEarningsStatsAction] Error:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to fetch earnings statistics",
    };
  }
}
