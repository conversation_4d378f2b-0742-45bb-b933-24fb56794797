"use client";

import { useState, useCallback } from "react";
import useS<PERSON> from "swr";
import { 
  getAdminSupportChats,
  getAdminSupportChatMessages,
  sendAdminSupportMessage,
  assignChatToAdmin,
  updateChatStatus,
  markAdminMessagesAsRead,
  getCustomerDetails
} from "@/actions/admin/customer-support";
import type { 
  SupportChatWithDetails, 
  SupportChatMessageWithSender 
} from "@/types/support-chat";

// Types for admin support chat
interface AdminSupportChatFilters {
  status?: "active" | "resolved" | "closed";
  type?: "vehicle" | "general";
  search?: string;
  assignedToMe?: boolean;
}

interface UseAdminSupportChatsResult {
  chats: SupportChatWithDetails[];
  isLoading: boolean;
  error: any;
  mutate: () => void;
  totalUnread: number;
}

interface UseAdminSupportChatMessagesResult {
  messages: SupportChatMessageWithSender[];
  isLoading: boolean;
  error: any;
  mutate: () => void;
}

interface UseAdminSupportChatActionsResult {
  sendMessage: (data: { chatId: number; content: string; messageType?: "text" | "image" | "document" }) => Promise<{ success: boolean; error?: string; messageId?: number }>;
  assignChat: (chatId: number, adminPartyId?: number) => Promise<{ success: boolean; error?: string }>;
  updateStatus: (chatId: number, status: "active" | "resolved" | "closed") => Promise<{ success: boolean; error?: string }>;
  markAsRead: (chatId: number) => Promise<{ success: boolean; error?: string }>;
}

interface CustomerDetailsResult {
  customer: any;
  isLoading: boolean;
  error: any;
  mutate: () => void;
}

/**
 * Hook for fetching admin support chats with real-time updates
 */
export function useAdminSupportChats(filters: AdminSupportChatFilters = {}): UseAdminSupportChatsResult {
  const { data, error, isLoading, mutate } = useSWR(
    ['admin-support-chats', JSON.stringify(filters)],
    () => getAdminSupportChats(filters),
    {
      refreshInterval: 5000, // Refresh every 5 seconds for real-time updates
      revalidateOnFocus: true,
      revalidateOnReconnect: true,
      dedupingInterval: 2000, // Avoid too frequent requests
    }
  );

  const chats = data?.success ? data.chats : [];
  const totalUnread = chats.reduce((sum, chat) => sum + (chat.unreadCount || 0), 0);

  return {
    chats,
    isLoading,
    error: data?.success === false ? data.error : error,
    mutate,
    totalUnread
  };
}

/**
 * Hook for fetching messages in a specific chat
 */
export function useAdminSupportChatMessages(chatId: number | null): UseAdminSupportChatMessagesResult {
  const { data, error, isLoading, mutate } = useSWR(
    chatId ? ['admin-support-chat-messages', chatId] : null,
    () => chatId ? getAdminSupportChatMessages(chatId) : null,
    {
      refreshInterval: 2000, // Refresh every 2 seconds for real-time messaging
      revalidateOnFocus: true,
      revalidateOnReconnect: true,
      dedupingInterval: 1000,
    }
  );

  const messages = data?.success ? data.messages : [];

  return {
    messages,
    isLoading,
    error: data?.success === false ? data.error : error,
    mutate
  };
}

/**
 * Hook for admin support chat actions
 */
export function useAdminSupportChatActions(): UseAdminSupportChatActionsResult {
  const sendMessage = useCallback(async (data: { 
    chatId: number; 
    content: string; 
    messageType?: "text" | "image" | "document" 
  }) => {
    try {
      const result = await sendAdminSupportMessage(data);
      return result;
    } catch (error) {
      console.error("Error sending message:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to send message"
      };
    }
  }, []);

  const assignChat = useCallback(async (chatId: number, adminPartyId?: number) => {
    try {
      const result = await assignChatToAdmin(chatId, adminPartyId);
      return result;
    } catch (error) {
      console.error("Error assigning chat:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to assign chat"
      };
    }
  }, []);

  const updateStatus = useCallback(async (chatId: number, status: "active" | "resolved" | "closed") => {
    try {
      const result = await updateChatStatus(chatId, status);
      return result;
    } catch (error) {
      console.error("Error updating status:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to update status"
      };
    }
  }, []);

  const markAsRead = useCallback(async (chatId: number) => {
    try {
      const result = await markAdminMessagesAsRead(chatId);
      return result;
    } catch (error) {
      console.error("Error marking as read:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to mark as read"
      };
    }
  }, []);

  return {
    sendMessage,
    assignChat,
    updateStatus,
    markAsRead
  };
}

/**
 * Hook for getting customer details
 */
export function useCustomerDetails(driverPartyId: number | null): CustomerDetailsResult {
  const { data, error, isLoading, mutate } = useSWR(
    driverPartyId ? ['customer-details', driverPartyId] : null,
    () => driverPartyId ? getCustomerDetails(driverPartyId) : null,
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: true,
      dedupingInterval: 60000, // Customer details don't change often
    }
  );

  const customer = data?.success ? data.customer : null;

  return {
    customer,
    isLoading,
    error: data?.success === false ? data.error : error,
    mutate
  };
}

/**
 * Hook for managing chat filters with local state
 */
export function useAdminChatFilters() {
  const [filters, setFilters] = useState<AdminSupportChatFilters>({
    status: undefined,
    type: undefined,
    search: "",
    assignedToMe: false
  });

  const updateFilter = useCallback((key: keyof AdminSupportChatFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  }, []);

  const clearFilters = useCallback(() => {
    setFilters({
      status: undefined,
      type: undefined,
      search: "",
      assignedToMe: false
    });
  }, []);

  return {
    filters,
    updateFilter,
    clearFilters
  };
}

/**
 * Hook for managing selected chat state
 */
export function useAdminChatSelection() {
  const [selectedChat, setSelectedChat] = useState<SupportChatWithDetails | null>(null);
  const [customerDetailsTab, setCustomerDetailsTab] = useState<string>("profile");

  const selectChat = useCallback((chat: SupportChatWithDetails | null) => {
    setSelectedChat(chat);
    setCustomerDetailsTab("profile"); // Reset to profile tab when selecting new chat
  }, []);

  return {
    selectedChat,
    customerDetailsTab,
    selectChat,
    setCustomerDetailsTab
  };
}