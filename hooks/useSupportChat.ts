"use client";
import { useMemo, useRef, useCallback } from "react";
import useS<PERSON> from "swr";
import { useAuthenticator } from "@aws-amplify/ui-react";
import { 
  getSupportChats, 
  getSupportChatMessages, 
  getDriverVehicles,
  createSupport<PERSON>hat,
  sendSupportChatMessage,
  updateSupportChatStatus,
  markMessagesAsRead
} from "@/actions/support-chat";
import type { 
  SupportChatWithDetails, 
  SupportChatMessageWithSender, 
  SupportChatFilters 
} from "@/types/support-chat";

interface UseSupportChatsResult {
  chats: SupportChatWithDetails[];
  isLoading: boolean;
  error: any;
  mutate: () => void;
  totalUnread: number;
}

interface UseSupportChatMessagesResult {
  messages: SupportChatMessageWithSender[];
  isLoading: boolean;
  error: any;
  mutate: () => void;
}

interface Vehicle {
  id: number;
  make: string;
  model: string;
  year: number;
  licensePlate?: string;
}

// Hook for fetching user's support chats
export function useSupportChats(filters: SupportChatFilters = {}): UseSupportChatsResult {
  const { user, authStatus } = useAuthenticator((context) => [context.user, context.authStatus]);
  const shouldFetch = authStatus === 'authenticated' && user;

  const { data, error, isLoading, mutate } = useSWR(
    shouldFetch ? ['support-chats', JSON.stringify(filters)] : null,
    () => getSupportChats(filters),
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: true,
      refreshInterval: 10000, // 10 seconds as requested
      errorRetryCount: 3,
      errorRetryInterval: 5000,
      keepPreviousData: true,
    }
  );

  const chats = useMemo<SupportChatWithDetails[]>(() => {
    if (!data?.success) return [];
    return (data.chats || []) as unknown as SupportChatWithDetails[];
  }, [data]);

  const totalUnread = useMemo(() => {
    // Simplified - no unread counts for now
    return 0;
  }, []);

  return {
    chats,
    isLoading: shouldFetch ? isLoading : false,
    error: (data as any)?.error ?? error,
    mutate,
    totalUnread,
  };
}

// Hook for fetching messages in a specific chat
export function useSupportChatMessages(chatId: number | null): UseSupportChatMessagesResult {
  const { user, authStatus } = useAuthenticator((context) => [context.user, context.authStatus]);
  const shouldFetch = authStatus === 'authenticated' && user && chatId;

  const { data, error, isLoading, mutate } = useSWR(
    shouldFetch ? ['support-chat-messages', chatId] : null,
    () => getSupportChatMessages(chatId!),
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: true,
      refreshInterval: 5000, // More frequent for active chat
      errorRetryCount: 3,
      errorRetryInterval: 5000,
      keepPreviousData: true,
    }
  );

  const messages = useMemo<SupportChatMessageWithSender[]>(() => {
    if (!data?.success) return [];
    return (data.messages || []) as unknown as SupportChatMessageWithSender[];
  }, [data]);

  return {
    messages,
    isLoading: shouldFetch ? isLoading : false,
    error: (data as any)?.error ?? error,
    mutate,
  };
}

// Hook for fetching driver's vehicles
export function useDriverVehicles(driverPartyId: number | null) {
  const { user, authStatus } = useAuthenticator((context) => [context.user, context.authStatus]);
  const shouldFetch = authStatus === 'authenticated' && user && driverPartyId;

  const { data, error, isLoading } = useSWR(
    shouldFetch ? ['driver-vehicles', driverPartyId] : null,
    () => getDriverVehicles(driverPartyId!),
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: true,
      refreshInterval: 60000, // Vehicles don't change often
      errorRetryCount: 3,
      errorRetryInterval: 5000,
    }
  );

  const vehicles = useMemo(() => {
    if (!data?.success) return [];
    const vehicleList = data.vehicles || [];
    return Array.isArray(vehicleList) ? vehicleList : [];
  }, [data]);

  return {
    vehicles,
    isLoading: shouldFetch ? isLoading : false,
    error: (data as any)?.error ?? error,
  };
}

// Sound notification hook
export function useSoundNotification() {
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const lastMessageCountRef = useRef<number>(0);

  // Initialize audio
  const initializeAudio = useCallback(() => {
    if (typeof window !== 'undefined' && !audioRef.current) {
      // Try to use a simple data URL for a beep sound instead of a file
      // This creates a simple notification beep
      audioRef.current = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmgfCT2X2e3KdSgGKHfH7+KNRA==');
      audioRef.current.volume = 0.3;
    }
  }, []);

  // Play notification sound
  const playNotificationSound = useCallback(() => {
    initializeAudio();
    if (audioRef.current) {
      audioRef.current.currentTime = 0;
      audioRef.current.play().catch((error) => {
        console.log('Could not play notification sound:', error);
      });
    }
  }, [initializeAudio]);

  // Check for new messages and play sound
  const checkForNewMessages = useCallback((messages: SupportChatMessageWithSender[], currentUserId: number) => {
    if (!messages || messages.length === 0) return;

    const newMessageCount = messages.length;
    const hasNewMessages = newMessageCount > lastMessageCountRef.current;
    
    if (hasNewMessages && lastMessageCountRef.current > 0) {
      // Check if the latest message is not from the current user
      const latestMessage = messages[messages.length - 1];
      if (latestMessage && latestMessage.senderPartyId !== currentUserId) {
        playNotificationSound();
      }
    }
    
    lastMessageCountRef.current = newMessageCount;
  }, [playNotificationSound]);

  return {
    playNotificationSound,
    checkForNewMessages,
  };
}

// Chat actions hook
export function useSupportChatActions() {
  const createChat = useCallback(async (data: {
    type: "vehicle" | "general";
    subject: string;
    vehicleId?: number;
    initialMessage: string;
    driverPartyId: number;
  }) => {
    try {
      const result = await createSupportChat(data);
      return result;
    } catch (error) {
      console.error('Error creating support chat:', error);
      return { success: false, error: 'Failed to create chat' };
    }
  }, []);

  const sendMessage = useCallback(async (data: {
    chatId: number;
    content: string;
    messageType?: "text" | "image" | "document";
    metadata?: any;
    senderPartyId: number;
  }) => {
    try {
      const result = await sendSupportChatMessage(data);
      return result;
    } catch (error) {
      console.error('Error sending message:', error);
      return { success: false, error: 'Failed to send message' };
    }
  }, []);

  const updateStatus = useCallback(async (
    chatId: number, 
    status: "active" | "resolved" | "closed",
    adminPartyId?: number
  ) => {
    try {
      const result = await updateSupportChatStatus(chatId, status, adminPartyId);
      return result;
    } catch (error) {
      console.error('Error updating chat status:', error);
      return { success: false, error: 'Failed to update status' };
    }
  }, []);

  const markAsRead = useCallback(async (chatId: number, partyId: number) => {
    try {
      const result = await markMessagesAsRead(chatId, partyId);
      return result;
    } catch (error) {
      console.error('Error marking messages as read:', error);
      return { success: false, error: 'Failed to mark as read' };
    }
  }, []);

  return {
    createChat,
    sendMessage,
    updateStatus,
    markAsRead,
  };
}