import { z } from "zod";
import {
  PaymentMethodEnum,
  PlatformNameEnum,
  DebtSourceTypeEnum,
} from "../types/payment-contract";

// =====================================================
// DEPOSIT PAYMENT FORM VALIDATION
// =====================================================

export const DepositPaymentFormSchema = z
  .object({
    assignmentId: z
      .number({
        required_error: "Assignment ID is required",
        invalid_type_error: "Assignment ID must be a number",
      })
      .positive("Assignment ID must be positive"),

    depositAmount: z
      .number({
        required_error: "Deposit amount is required",
        invalid_type_error: "Deposit amount must be a number",
      })
      .positive("Deposit amount must be greater than 0"),

    amountPaid: z
      .number({
        required_error: "Amount paid is required",
        invalid_type_error: "Amount paid must be a number",
      })
      .min(0, "Amount paid cannot be negative"),

    paymentMethod: PaymentMethodEnum.optional(),

    paymentReference: z
      .string()
      .min(1, "Payment reference cannot be empty")
      .max(100, "Payment reference too long")
      .optional(),

    notes: z.string().max(500, "Notes cannot exceed 500 characters").optional(),
  })
  .refine((data) => data.amountPaid <= data.depositAmount, {
    message: "Amount paid cannot exceed deposit amount",
    path: ["amountPaid"],
  })
  .refine((data) => !data.paymentReference || data.amountPaid > 0, {
    message:
      "Payment reference is only required when amount paid is greater than 0",
    path: ["paymentReference"],
  });

// =====================================================
// CONTRACT UPLOAD FORM VALIDATION
// =====================================================

export const ContractUploadFormSchema = z.object({
  assignmentId: z
    .number({
      required_error: "Assignment ID is required",
      invalid_type_error: "Assignment ID must be a number",
    })
    .positive("Assignment ID must be positive"),

  file: z
    .instanceof(File, {
      message: "File is required",
    })
    .refine((file) => file.size <= 10 * 1024 * 1024, {
      message: "File size must be less than 10MB",
    })
    .refine(
      (file) =>
        ["application/pdf", "image/jpeg", "image/png", "image/jpg"].includes(
          file.type
        ),
      {
        message: "File must be PDF, JPG, or PNG format",
      }
    ),

  notes: z.string().max(500, "Notes cannot exceed 500 characters").optional(),
});

// =====================================================
// WEEKLY EARNINGS FORM VALIDATION
// =====================================================

export const WeeklyEarningsFormSchema = z
  .object({
    assignmentId: z
      .number({
        required_error: "Assignment ID is required",
        invalid_type_error: "Assignment ID must be a number",
      })
      .positive("Assignment ID must be positive"),

    weekStartDate: z
      .string({
        required_error: "Week start date is required",
        invalid_type_error: "Week start date must be a string",
      })
      .regex(/^\d{4}-\d{2}-\d{2}$/, "Date must be in YYYY-MM-DD format")
      .refine((date) => !isNaN(Date.parse(date)), {
        message: "Invalid date format",
      }),
      // Temporarily disabled Monday validation for testing
      // .refine(
      //   (date) => {
      //     const startDate = new Date(date);
      //     return startDate.getDay() === 1; // Monday
      //   },
      //   {
      //     message: "Week start date must be a Monday",
      //   }
      // ),

    grossEarnings: z
      .number({
        required_error: "Gross earnings is required",
        invalid_type_error: "Gross earnings must be a number",
      })
      .min(0, "Gross earnings cannot be negative"),

    platformName: PlatformNameEnum.default("uber"),

    weeklyTarget: z
      .number({
        required_error: "Weekly target is required",
        invalid_type_error: "Weekly target must be a number",
      })
      .positive("Weekly target must be greater than 0")
      .default(2700),

    notes: z.string().max(500, "Notes cannot exceed 500 characters").optional(),
  })
  .refine(
    (data) => {
      const startDate = new Date(data.weekStartDate);
      const today = new Date();
      const oneWeekFromToday = new Date(
        today.getTime() + 7 * 24 * 60 * 60 * 1000
      );

      return startDate <= oneWeekFromToday;
    },
    {
      message: "Cannot record earnings for more than one week in the future",
      path: ["weekStartDate"],
    }
  );

// =====================================================
// PAYOUT PROCESSING FORM VALIDATION
// =====================================================

export const PayoutProcessingFormSchema = z.object({
  weeklyEarningsId: z
    .number({
      required_error: "Weekly earnings ID is required",
      invalid_type_error: "Weekly earnings ID must be a number",
    })
    .positive("Weekly earnings ID must be positive"),

  paymentMethod: PaymentMethodEnum.optional(),

  paymentReference: z
    .string()
    .min(1, "Payment reference cannot be empty")
    .max(100, "Payment reference too long")
    .optional(),

  notes: z.string().max(500, "Notes cannot exceed 500 characters").optional(),
});

// =====================================================
// DEBT ADJUSTMENT FORM VALIDATION
// =====================================================

export const DebtAdjustmentFormSchema = z.object({
  assignmentId: z
    .number({
      required_error: "Assignment ID is required",
      invalid_type_error: "Assignment ID must be a number",
    })
    .positive("Assignment ID must be positive"),

  adjustmentAmount: z
    .number({
      required_error: "Adjustment amount is required",
      invalid_type_error: "Adjustment amount must be a number",
    })
    .refine((amount) => amount !== 0, {
      message: "Adjustment amount cannot be zero",
    }),

  reason: z
    .string({
      required_error: "Reason is required",
      invalid_type_error: "Reason must be a string",
    })
    .min(5, "Reason must be at least 5 characters")
    .max(200, "Reason cannot exceed 200 characters"),

  notes: z.string().max(500, "Notes cannot exceed 500 characters").optional(),
});

// =====================================================
// DATE RANGE VALIDATION HELPERS
// =====================================================

export const DateRangeSchema = z
  .object({
    startDate: z
      .string()
      .regex(/^\d{4}-\d{2}-\d{2}$/, "Start date must be in YYYY-MM-DD format")
      .refine((date) => !isNaN(Date.parse(date)), {
        message: "Invalid start date format",
      }),

    endDate: z
      .string()
      .regex(/^\d{4}-\d{2}-\d{2}$/, "End date must be in YYYY-MM-DD format")
      .refine((date) => !isNaN(Date.parse(date)), {
        message: "Invalid end date format",
      }),
  })
  .refine((data) => new Date(data.startDate) <= new Date(data.endDate), {
    message: "Start date must be before or equal to end date",
    path: ["endDate"],
  });

// =====================================================
// FINANCIAL AMOUNT VALIDATION HELPERS
// =====================================================

export const MoneyAmountSchema = z
  .number({
    required_error: "Amount is required",
    invalid_type_error: "Amount must be a number",
  })
  .transform((amount) => {
    // Round to 2 decimal places for currency
    return Math.round(amount * 100) / 100;
  })
  .refine((amount) => amount >= 0, {
    message: "Amount cannot be negative",
  })
  .refine((amount) => amount <= 999999.99, {
    message: "Amount cannot exceed R999,999.99",
  });

// =====================================================
// SEARCH AND FILTER SCHEMAS
// =====================================================

export const PaymentSearchFiltersSchema = z
  .object({
    assignmentIds: z.array(z.number().positive()).optional(),
    startDate: z
      .string()
      .regex(/^\d{4}-\d{2}-\d{2}$/)
      .optional(),
    endDate: z
      .string()
      .regex(/^\d{4}-\d{2}-\d{2}$/)
      .optional(),
    paymentStatus: z
      .array(z.enum(["pending", "paid", "failed", "cancelled"]))
      .optional(),
    minAmount: z.number().min(0).optional(),
    maxAmount: z.number().min(0).optional(),
    platformName: z.array(PlatformNameEnum).optional(),
  })
  .refine(
    (data) => {
      if (data.startDate && data.endDate) {
        return new Date(data.startDate) <= new Date(data.endDate);
      }
      return true;
    },
    {
      message: "Start date must be before or equal to end date",
      path: ["endDate"],
    }
  )
  .refine(
    (data) => {
      if (data.minAmount && data.maxAmount) {
        return data.minAmount <= data.maxAmount;
      }
      return true;
    },
    {
      message: "Minimum amount must be less than or equal to maximum amount",
      path: ["maxAmount"],
    }
  );
