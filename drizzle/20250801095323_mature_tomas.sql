-- Create enums only if they don't exist
DO $$ BEGIN
    CREATE TYPE "public"."support_chat_message_type" AS ENUM('text', 'image', 'document', 'system');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;--> statement-breakpoint

DO $$ BEGIN
    CREATE TYPE "public"."support_chat_status" AS ENUM('active', 'resolved', 'closed');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;--> statement-breakpoint

DO $$ BEGIN
    CREATE TYPE "public"."support_chat_type" AS ENUM('vehicle', 'general');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;--> statement-breakpoint

DO $$ BEGIN
    CREATE TYPE "public"."assignment_status" AS ENUM('pending_setup', 'contract_uploaded', 'active', 'suspended', 'terminated');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;--> statement-breakpoint

DO $$ BEGIN
    CREATE TYPE "public"."payment_status" AS ENUM('pending', 'paid', 'failed', 'cancelled');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;--> statement-breakpoint

DO $$ BEGIN
    CREATE TYPE "public"."debt_source_type" AS ENUM('earnings_shortfall', 'debt_recovery', 'adjustment');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;--> statement-breakpoint

DO $$ BEGIN
    CREATE TYPE "public"."payment_method" AS ENUM('bank_transfer', 'cash', 'eft', 'card', 'other');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;--> statement-breakpoint

DO $$ BEGIN
    CREATE TYPE "public"."platform_name" AS ENUM('uber', 'bolt', 'indriver', 'other');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;--> statement-breakpoint
-- Add enum values only if they don't exist
DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_enum WHERE enumlabel = 'EHAILING_DRIVER' AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'company_purpose')) THEN
        ALTER TYPE "public"."company_purpose" ADD VALUE 'EHAILING_DRIVER';
    END IF;
END $$;--> statement-breakpoint

DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_enum WHERE enumlabel = 'superseded' AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'document_status')) THEN
        ALTER TYPE "public"."document_status" ADD VALUE 'superseded';
    END IF;
END $$;--> statement-breakpoint

DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_enum WHERE enumlabel = 'EHAILING_DRIVER' AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'group_purpose')) THEN
        ALTER TYPE "public"."group_purpose" ADD VALUE 'EHAILING_DRIVER';
    END IF;
END $$;--> statement-breakpoint

DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_enum WHERE enumlabel = 'MANDATORY_REGULAR_VEHICLE_INSPECTION' AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'task_type')) THEN
        ALTER TYPE "public"."task_type" ADD VALUE 'MANDATORY_REGULAR_VEHICLE_INSPECTION';
    END IF;
END $$;--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "support_chat_messages" (
	"id" serial PRIMARY KEY NOT NULL,
	"chat_id" integer NOT NULL,
	"sender_party_id" integer NOT NULL,
	"message_type" "support_chat_message_type" DEFAULT 'text' NOT NULL,
	"content" text NOT NULL,
	"metadata" jsonb,
	"is_read" boolean DEFAULT false NOT NULL,
	"read_at" timestamp with time zone,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "support_chat_participants" (
	"id" serial PRIMARY KEY NOT NULL,
	"chat_id" integer NOT NULL,
	"party_id" integer NOT NULL,
	"role" varchar(20) NOT NULL,
	"joined_at" timestamp with time zone DEFAULT now(),
	"last_read_at" timestamp with time zone,
	"created_at" timestamp with time zone DEFAULT now(),
	CONSTRAINT "support_chat_participants_unique" UNIQUE("chat_id","party_id")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "support_chats" (
	"id" serial PRIMARY KEY NOT NULL,
	"driver_party_id" integer NOT NULL,
	"admin_party_id" integer,
	"vehicle_id" integer,
	"type" "support_chat_type" NOT NULL,
	"subject" varchar(255) NOT NULL,
	"status" "support_chat_status" DEFAULT 'active' NOT NULL,
	"priority" varchar(20) DEFAULT 'normal',
	"last_message_at" timestamp with time zone,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "h_assignment_contracts" (
	"id" serial PRIMARY KEY NOT NULL,
	"assignment_id" integer NOT NULL,
	"contract_file_path" text NOT NULL,
	"original_filename" text NOT NULL,
	"file_size" integer NOT NULL,
	"mime_type" text NOT NULL,
	"uploaded_at" timestamp with time zone DEFAULT now() NOT NULL,
	"uploaded_by" integer NOT NULL,
	"replaced_by" integer,
	"notes" text
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "h_assignment_events" (
	"id" serial PRIMARY KEY NOT NULL,
	"assignment_id" integer NOT NULL,
	"event_type" text NOT NULL,
	"event_data" jsonb,
	"event_description" text,
	"performed_by" integer NOT NULL,
	"performed_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "h_assignment_initiation_fee" (
	"id" serial PRIMARY KEY NOT NULL,
	"assignment_id" integer NOT NULL,
	"total_fee_amount" numeric(10, 2) NOT NULL,
	"paid_amount" numeric(10, 2) DEFAULT '0' NOT NULL,
	"outstanding_amount" numeric(10, 2) NOT NULL,
	"payment_method" text,
	"payment_reference" text,
	"payment_date" timestamp with time zone,
	"status" text DEFAULT 'pending' NOT NULL,
	"due_date" timestamp with time zone,
	"notes" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "h_assignments" (
	"id" serial PRIMARY KEY NOT NULL,
	"group_id" integer NOT NULL,
	"driver_party_id" integer NOT NULL,
	"vehicle_id" integer NOT NULL,
	"admin_party_id" integer NOT NULL,
	"status" "assignment_status" DEFAULT 'active' NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone,
	"effective_from" timestamp with time zone DEFAULT now() NOT NULL,
	"effective_to" timestamp with time zone DEFAULT 'infinity'::timestamp NOT NULL,
	"terminated_at" timestamp with time zone,
	"terminated_by" integer,
	"termination_reason" text,
	"metadata" jsonb,
	CONSTRAINT "h_assignments_group_id_unique" UNIQUE("group_id")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "h_driver_payouts" (
	"id" serial PRIMARY KEY NOT NULL,
	"assignment_id" integer NOT NULL,
	"weekly_earnings_id" integer NOT NULL,
	"gross_payout" numeric(10, 2) NOT NULL,
	"debt_deduction" numeric(10, 2) DEFAULT '0' NOT NULL,
	"net_payout" numeric(10, 2) NOT NULL,
	"payment_method" "payment_method",
	"payment_reference" text,
	"payment_date" timestamp with time zone,
	"processed_by" integer NOT NULL,
	"status" "payment_status" DEFAULT 'pending' NOT NULL,
	"notes" text
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "h_weekly_earnings" (
	"id" serial PRIMARY KEY NOT NULL,
	"assignment_id" integer NOT NULL,
	"week_start_date" date NOT NULL,
	"week_end_date" date NOT NULL,
	"gross_earnings" numeric(10, 2) NOT NULL,
	"platform_name" "platform_name" DEFAULT 'uber' NOT NULL,
	"weekly_target" numeric(10, 2) DEFAULT '2700' NOT NULL,
	"earnings_shortfall" numeric(10, 2) DEFAULT '0' NOT NULL,
	"recorded_by" integer NOT NULL,
	"notes" text,
	CONSTRAINT "unique_assignment_week" UNIQUE("assignment_id","week_start_date")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "h_assignment_deposits" (
	"id" serial PRIMARY KEY NOT NULL,
	"assignment_id" integer NOT NULL,
	"deposit_amount" numeric(10, 2) NOT NULL,
	"amount_paid" numeric(10, 2) DEFAULT '0' NOT NULL,
	"balance_remaining" numeric(10, 2) NOT NULL,
	"payment_method" "payment_method",
	"payment_reference" text,
	"payment_date" timestamp with time zone,
	"notes" text
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "h_driver_debt_tracking" (
	"id" serial PRIMARY KEY NOT NULL,
	"assignment_id" integer NOT NULL,
	"debt_source_id" integer NOT NULL,
	"debt_source_type" "debt_source_type" NOT NULL,
	"debt_amount" numeric(10, 2) NOT NULL,
	"running_balance" numeric(10, 2) NOT NULL,
	"transaction_date" timestamp with time zone DEFAULT now() NOT NULL,
	"notes" text
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "h_payment_audit_log" (
	"id" serial PRIMARY KEY NOT NULL,
	"assignment_id" integer NOT NULL,
	"action_type" text NOT NULL,
	"table_name" text NOT NULL,
	"record_id" integer NOT NULL,
	"old_values" jsonb,
	"new_values" jsonb,
	"performed_at" timestamp with time zone DEFAULT now() NOT NULL,
	"performed_by" integer NOT NULL,
	"ip_address" text,
	"user_agent" text
);
--> statement-breakpoint
-- Add foreign key constraints only if they don't exist
DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'support_chat_messages_chat_id_support_chats_id_fk') THEN
        ALTER TABLE "support_chat_messages" ADD CONSTRAINT "support_chat_messages_chat_id_support_chats_id_fk" FOREIGN KEY ("chat_id") REFERENCES "public"."support_chats"("id") ON DELETE cascade ON UPDATE no action;
    END IF;
END $$;--> statement-breakpoint

DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'support_chat_messages_sender_party_id_party_id_fk') THEN
        ALTER TABLE "support_chat_messages" ADD CONSTRAINT "support_chat_messages_sender_party_id_party_id_fk" FOREIGN KEY ("sender_party_id") REFERENCES "public"."party"("id") ON DELETE cascade ON UPDATE no action;
    END IF;
END $$;--> statement-breakpoint

DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'support_chat_participants_chat_id_support_chats_id_fk') THEN
        ALTER TABLE "support_chat_participants" ADD CONSTRAINT "support_chat_participants_chat_id_support_chats_id_fk" FOREIGN KEY ("chat_id") REFERENCES "public"."support_chats"("id") ON DELETE cascade ON UPDATE no action;
    END IF;
END $$;--> statement-breakpoint

DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'support_chat_participants_party_id_party_id_fk') THEN
        ALTER TABLE "support_chat_participants" ADD CONSTRAINT "support_chat_participants_party_id_party_id_fk" FOREIGN KEY ("party_id") REFERENCES "public"."party"("id") ON DELETE cascade ON UPDATE no action;
    END IF;
END $$;--> statement-breakpoint

DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'support_chats_driver_party_id_party_id_fk') THEN
        ALTER TABLE "support_chats" ADD CONSTRAINT "support_chats_driver_party_id_party_id_fk" FOREIGN KEY ("driver_party_id") REFERENCES "public"."party"("id") ON DELETE cascade ON UPDATE no action;
    END IF;
END $$;--> statement-breakpoint

DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'support_chats_admin_party_id_party_id_fk') THEN
        ALTER TABLE "support_chats" ADD CONSTRAINT "support_chats_admin_party_id_party_id_fk" FOREIGN KEY ("admin_party_id") REFERENCES "public"."party"("id") ON DELETE set null ON UPDATE no action;
    END IF;
END $$;--> statement-breakpoint

DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'support_chats_vehicle_id_vehicles_id_fk') THEN
        ALTER TABLE "support_chats" ADD CONSTRAINT "support_chats_vehicle_id_vehicles_id_fk" FOREIGN KEY ("vehicle_id") REFERENCES "public"."vehicles"("id") ON DELETE set null ON UPDATE no action;
    END IF;
END $$;--> statement-breakpoint
-- Add remaining foreign key constraints only if they don't exist
DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'h_assignment_contracts_uploaded_by_party_id_fk') THEN
        ALTER TABLE "h_assignment_contracts" ADD CONSTRAINT "h_assignment_contracts_uploaded_by_party_id_fk" FOREIGN KEY ("uploaded_by") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;
    END IF;
END $$;--> statement-breakpoint

DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'h_assignment_events_assignment_id_h_assignments_id_fk') THEN
        ALTER TABLE "h_assignment_events" ADD CONSTRAINT "h_assignment_events_assignment_id_h_assignments_id_fk" FOREIGN KEY ("assignment_id") REFERENCES "public"."h_assignments"("id") ON DELETE no action ON UPDATE no action;
    END IF;
END $$;--> statement-breakpoint

DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'h_assignment_events_performed_by_party_id_fk') THEN
        ALTER TABLE "h_assignment_events" ADD CONSTRAINT "h_assignment_events_performed_by_party_id_fk" FOREIGN KEY ("performed_by") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;
    END IF;
END $$;--> statement-breakpoint

DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'h_assignment_initiation_fee_assignment_id_h_assignments_id_fk') THEN
        ALTER TABLE "h_assignment_initiation_fee" ADD CONSTRAINT "h_assignment_initiation_fee_assignment_id_h_assignments_id_fk" FOREIGN KEY ("assignment_id") REFERENCES "public"."h_assignments"("id") ON DELETE no action ON UPDATE no action;
    END IF;
END $$;--> statement-breakpoint

DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'h_assignments_driver_party_id_party_id_fk') THEN
        ALTER TABLE "h_assignments" ADD CONSTRAINT "h_assignments_driver_party_id_party_id_fk" FOREIGN KEY ("driver_party_id") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;
    END IF;
END $$;--> statement-breakpoint

DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'h_assignments_admin_party_id_party_id_fk') THEN
        ALTER TABLE "h_assignments" ADD CONSTRAINT "h_assignments_admin_party_id_party_id_fk" FOREIGN KEY ("admin_party_id") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;
    END IF;
END $$;--> statement-breakpoint

DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'h_assignments_terminated_by_party_id_fk') THEN
        ALTER TABLE "h_assignments" ADD CONSTRAINT "h_assignments_terminated_by_party_id_fk" FOREIGN KEY ("terminated_by") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;
    END IF;
END $$;--> statement-breakpoint

DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'h_driver_payouts_weekly_earnings_id_h_weekly_earnings_id_fk') THEN
        ALTER TABLE "h_driver_payouts" ADD CONSTRAINT "h_driver_payouts_weekly_earnings_id_h_weekly_earnings_id_fk" FOREIGN KEY ("weekly_earnings_id") REFERENCES "public"."h_weekly_earnings"("id") ON DELETE no action ON UPDATE no action;
    END IF;
END $$;--> statement-breakpoint

DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'h_driver_payouts_processed_by_party_id_fk') THEN
        ALTER TABLE "h_driver_payouts" ADD CONSTRAINT "h_driver_payouts_processed_by_party_id_fk" FOREIGN KEY ("processed_by") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;
    END IF;
END $$;--> statement-breakpoint

DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'h_weekly_earnings_recorded_by_party_id_fk') THEN
        ALTER TABLE "h_weekly_earnings" ADD CONSTRAINT "h_weekly_earnings_recorded_by_party_id_fk" FOREIGN KEY ("recorded_by") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;
    END IF;
END $$;--> statement-breakpoint

DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'h_payment_audit_log_performed_by_party_id_fk') THEN
        ALTER TABLE "h_payment_audit_log" ADD CONSTRAINT "h_payment_audit_log_performed_by_party_id_fk" FOREIGN KEY ("performed_by") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;
    END IF;
END $$;--> statement-breakpoint
-- Create indexes only if they don't exist
CREATE INDEX IF NOT EXISTS "idx_support_chat_messages_chat" ON "support_chat_messages" USING btree ("chat_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_support_chat_messages_sender" ON "support_chat_messages" USING btree ("sender_party_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_support_chat_messages_created_at" ON "support_chat_messages" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_support_chat_messages_unread" ON "support_chat_messages" USING btree ("is_read","chat_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_support_chat_participants_chat" ON "support_chat_participants" USING btree ("chat_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_support_chat_participants_party" ON "support_chat_participants" USING btree ("party_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_support_chats_driver" ON "support_chats" USING btree ("driver_party_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_support_chats_admin" ON "support_chats" USING btree ("admin_party_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_support_chats_status" ON "support_chats" USING btree ("status");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_support_chats_vehicle" ON "support_chats" USING btree ("vehicle_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_support_chats_created_at" ON "support_chats" USING btree ("created_at");