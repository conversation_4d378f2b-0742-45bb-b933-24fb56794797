import {
  pgTable,
  serial,
  integer,
  decimal,
  text,
  timestamp,
  boolean,
  jsonb,
  pgEnum,
} from "drizzle-orm/pg-core";
import { sql } from "drizzle-orm";
import { party } from "../schema";
// import { temporalFields } from "./temporals";

// Assignment status enum
export const assignmentStatusEnum = pgEnum("assignment_status", [
  "pending_setup",
  "contract_uploaded", 
  "active",
  "suspended",
  "terminated",
]);

// Payment status enum  
export const paymentStatusEnum = pgEnum("payment_status", [
  "current",
  "pending", 
  "overdue",
  "defaulted",
]);

// H_Assignments - Clean bridging table with strategic duplicate data for performance
// Central point for all assignment queries with minimal joins
export const h_assignments = pgTable("h_assignments", {
  id: serial().primaryKey().notNull(),

  // Link to existing groups system (one-to-one relationship)
  groupId: integer("group_id").notNull().unique(), // References groups.id

  // Strategic duplicate data for query performance (denormalized)
  driverPartyId: integer("driver_party_id")
    .references(() => party.id)
    .notNull(),
  vehicleId: integer("vehicle_id").notNull(), // References vehicles.id
  adminPartyId: integer("admin_party_id")
    .references(() => party.id)
    .notNull(),

  // Assignment status and lifecycle
  status: assignmentStatusEnum("status")
    .default("active")
    .notNull(),

  // Temporal fields
  createdAt: timestamp("created_at", { withTimezone: true })
    .defaultNow()
    .notNull(),
  updatedAt: timestamp("updated_at", { withTimezone: true }),
  effectiveFrom: timestamp("effective_from", { withTimezone: true })
    .defaultNow()
    .notNull(),
  effectiveTo: timestamp("effective_to", { withTimezone: true })
    .default(sql`'infinity'::timestamp`)
    .notNull(),

  // Termination details
  terminatedAt: timestamp("terminated_at", { withTimezone: true }),
  terminatedBy: integer("terminated_by").references(() => party.id),
  terminationReason: text("termination_reason"),

  // Additional metadata as JSON
  metadata: jsonb("metadata"), // For flexible additional data
});

// H_Assignment_Events - Track all assignment lifecycle events
export const h_assignment_events = pgTable("h_assignment_events", {
  id: serial().primaryKey().notNull(),
  assignmentId: integer("assignment_id")
    .references(() => h_assignments.id)
    .notNull(),
  
  eventType: text("event_type").notNull(), // 'created', 'activated', 'suspended', 'terminated', 'contract_uploaded', 'payment_made', etc.
  eventData: jsonb("event_data"), // Flexible event details
  eventDescription: text("event_description"),
  
  performedBy: integer("performed_by")
    .references(() => party.id)
    .notNull(),
  performedAt: timestamp("performed_at", { withTimezone: true })
    .defaultNow()
    .notNull(),
  
  // Temporal fields
  // ...temporalFields,
});

// H_Assignment_Contracts - Contract document management
export const h_assignment_contracts = pgTable("h_assignment_contracts", {
  id: serial().primaryKey().notNull(),
  assignmentId: integer("assignment_id")
    .references(() => h_assignments.id)
    .notNull(),

  contractFilePath: text("contract_file_path").notNull(),
  originalFilename: text("original_filename").notNull(),
  fileSize: integer("file_size").notNull(),
  mimeType: text("mime_type").notNull(),

  uploadedAt: timestamp("uploaded_at", { withTimezone: true })
    .defaultNow()
    .notNull(),
  uploadedBy: integer("uploaded_by")
    .references(() => party.id)
    .notNull(),

  // Contract versioning
  replacedBy: integer("replaced_by"), // Self-reference to h_assignment_contracts.id
  isActive: boolean("is_active").default(true).notNull(),

  notes: text("notes"),

  // Temporal fields
  createdAt: timestamp("created_at", { withTimezone: true })
    .defaultNow()
    .notNull(),
  updatedAt: timestamp("updated_at", { withTimezone: true }),
});

// H_Assignment_Initiation_Fee - Track initiation fee payments
export const h_assignment_initiation_fee = pgTable("h_assignment_initiation_fee", {
  id: serial().primaryKey().notNull(),
  assignmentId: integer("assignment_id")
    .references(() => h_assignments.id)
    .notNull(),

  // Fee structure
  totalFeeAmount: decimal("total_fee_amount", { precision: 10, scale: 2 }).notNull(),
  paidAmount: decimal("paid_amount", { precision: 10, scale: 2 })
    .default("0")
    .notNull(),
  outstandingAmount: decimal("outstanding_amount", { precision: 10, scale: 2 }).notNull(),

  // Payment tracking
  paymentMethod: text("payment_method"), // 'cash', 'bank_transfer', 'card', etc.
  paymentReference: text("payment_reference"),
  paymentDate: timestamp("payment_date", { withTimezone: true }),

  // Status
  status: text("status").default("pending").notNull(), // 'pending', 'partial', 'paid', 'overdue'
  dueDate: timestamp("due_date", { withTimezone: true }),

  notes: text("notes"),

  // Temporal fields
  createdAt: timestamp("created_at", { withTimezone: true })
    .defaultNow()
    .notNull(),
  updatedAt: timestamp("updated_at", { withTimezone: true }),
});

// H_Weekly_Earnings - Track weekly earnings and targets
export const h_weekly_earnings = pgTable("h_weekly_earnings", {
  id: serial().primaryKey().notNull(),
  assignmentId: integer("assignment_id")
    .references(() => h_assignments.id)
    .notNull(),

  // Week identification
  weekStartDate: timestamp("week_start_date", { withTimezone: true }).notNull(),
  weekEndDate: timestamp("week_end_date", { withTimezone: true }).notNull(),
  weekNumber: integer("week_number").notNull(), // Week of year
  year: integer("year").notNull(),

  // Financial targets and actuals
  weeklyTarget: decimal("weekly_target", { precision: 10, scale: 2 }).notNull(), // Expected weekly payment
  actualEarnings: decimal("actual_earnings", { precision: 10, scale: 2 })
    .default("0")
    .notNull(),
  shortfall: decimal("shortfall", { precision: 10, scale: 2 })
    .default("0")
    .notNull(), // Calculated: weeklyTarget - actualEarnings (if positive)

  // Performance metrics
  tripsCompleted: integer("trips_completed").default(0).notNull(),
  hoursWorked: decimal("hours_worked", { precision: 5, scale: 2 }).default("0").notNull(),
  averageEarningsPerTrip: decimal("avg_earnings_per_trip", { precision: 8, scale: 2 }),

  // Status and tracking
  status: text("status").default("active").notNull(), // 'active', 'completed', 'shortfall_recorded'
  paymentDueDate: timestamp("payment_due_date", { withTimezone: true }).notNull(),
  paymentReceivedDate: timestamp("payment_received_date", { withTimezone: true }),

  notes: text("notes"),

  // Temporal fields
  createdAt: timestamp("created_at", { withTimezone: true })
    .defaultNow()
    .notNull(),
  updatedAt: timestamp("updated_at", { withTimezone: true }),
});

// H_Driver_Payouts - Track payments made to drivers
export const h_driver_payouts = pgTable("h_driver_payouts", {
  id: serial().primaryKey().notNull(),
  assignmentId: integer("assignment_id")
    .references(() => h_assignments.id)
    .notNull(),
  weeklyEarningsId: integer("weekly_earnings_id")
    .references(() => h_weekly_earnings.id), // Optional: link to specific week

  // Payout details
  payoutAmount: decimal("payout_amount", { precision: 10, scale: 2 }).notNull(),
  payoutType: text("payout_type").notNull(), // 'weekly_share', 'bonus', 'adjustment', 'refund'

  // Payment method and tracking
  paymentMethod: text("payment_method").notNull(), // 'bank_transfer', 'cash', 'mobile_money'
  paymentReference: text("payment_reference"),
  paymentDate: timestamp("payment_date", { withTimezone: true }).notNull(),

  // Period covered by this payout
  periodStartDate: timestamp("period_start_date", { withTimezone: true }),
  periodEndDate: timestamp("period_end_date", { withTimezone: true }),

  // Status and approval
  status: text("status").default("pending").notNull(), // 'pending', 'approved', 'paid', 'failed'
  approvedBy: integer("approved_by").references(() => party.id),
  approvedAt: timestamp("approved_at", { withTimezone: true }),

  notes: text("notes"),

  // Temporal fields
  createdAt: timestamp("created_at", { withTimezone: true })
    .defaultNow()
    .notNull(),
  updatedAt: timestamp("updated_at", { withTimezone: true }),
});
