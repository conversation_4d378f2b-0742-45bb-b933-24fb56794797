-- Phase 2: Connect Payment System to H_Assignments
-- This script updates existing payment tables to properly reference h_assignments.id

-- Step 1: Add foreign key constraints to existing payment tables
-- (These tables already have assignmentId columns, we just need to connect them)

-- Connect h_assignment_deposits to h_assignments
ALTER TABLE h_assignment_deposits 
ADD CONSTRAINT fk_assignment_deposits_assignment_id 
FOREIGN KEY (assignment_id) REFERENCES h_assignments(id);

-- Connect h_weekly_earnings to h_assignments  
ALTER TABLE h_weekly_earnings 
ADD CONSTRAINT fk_weekly_earnings_assignment_id 
FOREIGN KEY (assignment_id) REFERENCES h_assignments(id);

-- Connect h_driver_payouts to h_assignments
ALTER TABLE h_driver_payouts 
ADD CONSTRAINT fk_driver_payouts_assignment_id 
FOREIGN KEY (assignment_id) REFERENCES h_assignments(id);

-- Connect h_driver_debt_tracking to h_assignments
ALTER TABLE h_driver_debt_tracking 
ADD CONSTRAINT fk_driver_debt_assignment_id 
FOREIGN KEY (assignment_id) REFERENCES h_assignments(id);

-- Connect h_assignment_contracts to h_assignments
ALTER TABLE h_assignment_contracts 
ADD CONSTRAINT fk_assignment_contracts_assignment_id 
FOREIGN KEY (assignment_id) REFERENCES h_assignments(id);

-- Connect h_payment_audit_log to h_assignments
ALTER TABLE h_payment_audit_log 
ADD CONSTRAINT fk_payment_audit_assignment_id 
FOREIGN KEY (assignment_id) REFERENCES h_assignments(id);

-- Step 2: Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_assignment_deposits_assignment_id ON h_assignment_deposits(assignment_id);
CREATE INDEX IF NOT EXISTS idx_weekly_earnings_assignment_id ON h_weekly_earnings(assignment_id);
CREATE INDEX IF NOT EXISTS idx_driver_payouts_assignment_id ON h_driver_payouts(assignment_id);
CREATE INDEX IF NOT EXISTS idx_driver_debt_assignment_id ON h_driver_debt_tracking(assignment_id);
CREATE INDEX IF NOT EXISTS idx_assignment_contracts_assignment_id ON h_assignment_contracts(assignment_id);
CREATE INDEX IF NOT EXISTS idx_payment_audit_assignment_id ON h_payment_audit_log(assignment_id);

-- Step 3: Show current state
SELECT 
  'h_assignments' as table_name,
  COUNT(*) as record_count
FROM h_assignments
UNION ALL
SELECT 
  'h_assignment_deposits' as table_name,
  COUNT(*) as record_count  
FROM h_assignment_deposits
UNION ALL
SELECT 
  'h_weekly_earnings' as table_name,
  COUNT(*) as record_count
FROM h_weekly_earnings
UNION ALL
SELECT 
  'h_driver_payouts' as table_name,
  COUNT(*) as record_count
FROM h_driver_payouts
UNION ALL
SELECT 
  'h_driver_debt_tracking' as table_name,
  COUNT(*) as record_count
FROM h_driver_debt_tracking
UNION ALL
SELECT 
  'h_assignment_contracts' as table_name,
  COUNT(*) as record_count
FROM h_assignment_contracts;

-- Step 4: Show which payment records need assignment_id populated
SELECT 
  'Deposits without assignment_id' as issue,
  COUNT(*) as count
FROM h_assignment_deposits 
WHERE assignment_id IS NULL OR assignment_id = 0
UNION ALL
SELECT 
  'Earnings without assignment_id' as issue,
  COUNT(*) as count
FROM h_weekly_earnings 
WHERE assignment_id IS NULL OR assignment_id = 0
UNION ALL
SELECT 
  'Payouts without assignment_id' as issue,
  COUNT(*) as count
FROM h_driver_payouts 
WHERE assignment_id IS NULL OR assignment_id = 0;
