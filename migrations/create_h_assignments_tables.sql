-- Create assignment status enum
CREATE TYPE assignment_status AS ENUM (
  'pending_setup',
  'contract_uploaded', 
  'active',
  'suspended',
  'terminated'
);

-- Create payment status enum  
CREATE TYPE payment_status AS ENUM (
  'current',
  'pending', 
  'overdue',
  'defaulted'
);

-- Create H_Assignments table (clean bridging table)
CREATE TABLE h_assignments (
  id SERIAL PRIMARY KEY,

  -- Link to existing groups system (one-to-one relationship)
  group_id INTEGER NOT NULL UNIQUE, -- References groups.id

  -- Strategic duplicate data for query performance (denormalized)
  driver_party_id INTEGER NOT NULL REFERENCES party(id),
  vehicle_id INTEGER NOT NULL, -- References vehicles.id
  admin_party_id INTEGER NOT NULL REFERENCES party(id),

  -- Assignment status and lifecycle
  status assignment_status NOT NULL DEFAULT 'active',

  -- Temporal fields
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ,
  effective_from TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  effective_to TIMESTAMPTZ NOT NULL DEFAULT 'infinity'::timestamptz,

  -- Termination details
  terminated_at TIMESTAMPTZ,
  terminated_by INTEGER REFERENCES party(id),
  termination_reason TEXT,

  -- Additional metadata as JSON
  metadata JSONB
);

-- Create H_Assignment_Events table
CREATE TABLE h_assignment_events (
  id SERIAL PRIMARY KEY,
  assignment_id INTEGER NOT NULL REFERENCES h_assignments(id),
  
  event_type TEXT NOT NULL, -- 'created', 'activated', 'suspended', 'terminated', 'contract_uploaded', 'payment_made', etc.
  event_data JSONB, -- Flexible event details
  event_description TEXT,
  
  performed_by INTEGER NOT NULL REFERENCES party(id),
  performed_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  
  -- Temporal fields
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ,
  created_by INTEGER REFERENCES party(id),
  updated_by INTEGER REFERENCES party(id),
  effective_from TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  effective_to TIMESTAMPTZ NOT NULL DEFAULT 'infinity'::timestamptz
);

-- Create indexes for better performance
CREATE INDEX idx_h_assignments_group_id ON h_assignments(group_id);
CREATE INDEX idx_h_assignments_driver_party_id ON h_assignments(driver_party_id);
CREATE INDEX idx_h_assignments_vehicle_id ON h_assignments(vehicle_id);
CREATE INDEX idx_h_assignments_admin_party_id ON h_assignments(admin_party_id);
CREATE INDEX idx_h_assignments_status ON h_assignments(status);
CREATE INDEX idx_h_assignments_created_at ON h_assignments(created_at);
CREATE INDEX idx_h_assignments_effective_to ON h_assignments(effective_to);

CREATE INDEX idx_h_assignment_events_assignment_id ON h_assignment_events(assignment_id);
CREATE INDEX idx_h_assignment_events_event_type ON h_assignment_events(event_type);
CREATE INDEX idx_h_assignment_events_performed_at ON h_assignment_events(performed_at);

-- Add comments
COMMENT ON TABLE h_assignments IS 'Clean bridging table for vehicle-driver assignments (supplements existing groups system)';
COMMENT ON TABLE h_assignment_events IS 'Audit trail for all assignment lifecycle events';

COMMENT ON COLUMN h_assignments.group_id IS 'Reference to groups.id (one-to-one relationship)';
COMMENT ON COLUMN h_assignments.driver_party_id IS 'Strategic duplicate from groups for query performance';
COMMENT ON COLUMN h_assignments.vehicle_id IS 'Strategic duplicate from groups for query performance';
COMMENT ON COLUMN h_assignments.admin_party_id IS 'Strategic duplicate from groups for query performance';
COMMENT ON COLUMN h_assignments.status IS 'Assignment lifecycle status';
COMMENT ON COLUMN h_assignments.metadata IS 'Additional flexible data as JSON';

COMMENT ON COLUMN h_assignment_events.event_type IS 'Type of event (created, activated, suspended, etc.)';
COMMENT ON COLUMN h_assignment_events.event_data IS 'Additional event-specific data as JSON';

-- Populate h_assignments from existing groups data
INSERT INTO h_assignments (
  group_id,
  driver_party_id,
  vehicle_id,
  admin_party_id,
  status,
  created_at,
  effective_from
)
SELECT DISTINCT
  g.id as group_id,
  driver_gm.party_id as driver_party_id,
  gsv.vehicle_id,
  admin_gm.party_id as admin_party_id,
  'active' as status,
  g.created_at,
  g.created_at as effective_from
FROM groups g
-- Get admin membership
INNER JOIN group_memberships admin_gm ON g.id = admin_gm.group_id
  AND admin_gm.role = 'ADMIN'
  AND admin_gm.effective_to = 'infinity'::timestamptz
-- Get driver membership
INNER JOIN group_memberships driver_gm ON g.id = driver_gm.group_id
  AND driver_gm.role = 'DRIVER'
  AND driver_gm.effective_to = 'infinity'::timestamptz
-- Get shared vehicle
INNER JOIN group_shared_vehicles gsv ON g.id = gsv.group_id
  AND gsv.effective_to = 'infinity'::timestamptz
WHERE g.description LIKE '%vehicle assignment%'
   OR g.description LIKE '%Poolly managed%'
   OR g.initial_purpose = 'EHAILING'
ON CONFLICT (group_id) DO NOTHING;
