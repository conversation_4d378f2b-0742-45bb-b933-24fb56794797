// Debug script to check h_vehicle_catalog table schema
import { Pool } from 'pg';
import dotenv from 'dotenv';
dotenv.config();

async function checkSchema() {
  const pool = new Pool({
    connectionString: process.env.DATABASE_URL,
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : undefined,
  });

  try {
    console.log('🔍 Checking h_vehicle_catalog table schema...');
    
    const result = await pool.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'h_vehicle_catalog' 
      ORDER BY ordinal_position;
    `);

    console.log('\n📋 Current h_vehicle_catalog columns:');
    console.log('=====================================');
    result.rows.forEach(row => {
      console.log(`${row.column_name.padEnd(20)} | ${row.data_type.padEnd(15)} | ${row.is_nullable === 'YES' ? 'NULL' : 'NOT NULL'} | ${row.column_default || 'no default'}`);
    });

    // Check if table exists at all
    const tableExists = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'h_vehicle_catalog'
      );
    `);

    console.log(`\n✅ Table exists: ${tableExists.rows[0].exists}`);

    // Check for specific columns we need
    const requiredColumns = ['party_id', 'make_id', 'model_id', 'variant_id', 'weekly_rate', 'initiation_fee', 'is_active'];
    console.log('\n🔍 Checking for required columns:');
    
    for (const col of requiredColumns) {
      const colExists = result.rows.some(row => row.column_name === col);
      console.log(`${col.padEnd(20)} | ${colExists ? '✅ EXISTS' : '❌ MISSING'}`);
    }

  } catch (error) {
    console.error('❌ Error checking schema:', error.message);
  } finally {
    await pool.end();
  }
}

checkSchema();
