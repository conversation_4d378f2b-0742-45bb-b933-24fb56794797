-- Populate h_assignments from existing groups data
-- Run this after npm run db:push to populate the bridge table

INSERT INTO h_assignments (
  group_id,
  driver_party_id,
  vehicle_id,
  admin_party_id,
  status,
  created_at,
  effective_from
)
SELECT DISTINCT
  g.id as group_id,
  member_roles.party_id as driver_party_id,
  gsv.vehicle_id,
  admin_roles.party_id as admin_party_id,
  'active'::assignment_status as status,
  g.created_at,
  g.created_at as effective_from
FROM groups g
-- Get admin role (from group_member_roles table)
INNER JOIN group_member_roles admin_roles ON g.id = admin_roles.group_id
  AND admin_roles.role = 'ADMIN'
  AND admin_roles.effective_to = 'infinity'::timestamptz
-- Get member role (drivers are MEMBER role, not DRIVER)
INNER JOIN group_member_roles member_roles ON g.id = member_roles.group_id
  AND member_roles.role = 'MEMBER'
  AND member_roles.effective_to = 'infinity'::timestamptz
  AND member_roles.party_id != admin_roles.party_id  -- Ensure member is not the same as admin
-- Get shared vehicle
INNER JOIN group_shared_vehicles gsv ON g.id = gsv.group_id
  AND gsv.effective_to = 'infinity'::timestamptz
WHERE (
  g.description LIKE '%vehicle assignment%'
  OR g.description LIKE '%Poolly managed%'
  OR g.initial_purpose = 'EHAILING_DRIVER'
  OR EXISTS (
    -- Also include groups that have both admin and member with shared vehicle
    SELECT 1 FROM group_member_roles gmr1
    WHERE gmr1.group_id = g.id AND gmr1.role = 'ADMIN'
  )
)
ON CONFLICT (group_id) DO NOTHING;

-- Show results
SELECT 
  COUNT(*) as total_assignments_created,
  COUNT(DISTINCT admin_party_id) as unique_admins,
  COUNT(DISTINCT driver_party_id) as unique_drivers,
  COUNT(DISTINCT vehicle_id) as unique_vehicles
FROM h_assignments;

-- Show sample data
SELECT 
  ha.id,
  ha.group_id,
  ha.status,
  i.first_name || ' ' || i.last_name as driver_name,
  v.vehicle_registration
FROM h_assignments ha
LEFT JOIN party p ON ha.driver_party_id = p.id
LEFT JOIN individual i ON p.id = i.party_id
LEFT JOIN vehicles v ON ha.vehicle_id = v.id
LIMIT 5;
