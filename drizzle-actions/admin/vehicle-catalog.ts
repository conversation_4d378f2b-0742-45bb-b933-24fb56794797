"use server";

/**
 * DRIZZLE ACTIONS - ADMIN VEHICLE CATALOG
 *
 * This file contains all direct database operations for vehicle catalog management using Drizzle ORM.
 * Handles CRUD operations for h_vehicle_catalog and related tables (features, images, platforms).
 */

import { db } from "../../db";
import { eq, and, desc, asc, like, or, sql, inArray } from "drizzle-orm";
import {
  h_vehicleCatalog,
  h_vehicleCatalogFeatures,
  h_vehicleCatalogImages,
  h_vehicleCatalogPlatforms,
} from "@/drizzle/h_schema/vehicle-catalog";
import {
  h_listings,
  h_listing_approval_status,
  h_listing_publish_status,
} from "@/drizzle/h_schema/listings";
import { getUserAttributes } from "@/lib/serverUserAttributes";

// ==================== TYPES ====================

export interface VehicleCatalogItem {
  id: number;
  partyId: number;
  // Foreign key fields
  makeId?: number;
  modelId?: number;
  variantId?: number;
  // Text fields
  make: string;
  model: string;
  year: number;
  variant?: string;
  category: string;
  fuelType: string;
  ehailingEligible: boolean;
  // Pricing fields
  estimatedPrice?: number;
  weeklyFeeTarget?: number;
  weeklyRate?: number; // ✅ Now exists in database
  initiationFee?: number; // ✅ Now exists in database
  // Content fields
  description?: string;
  specifications?: string;
  // Status field
  isActive: boolean; // ✅ Now exists in database
  isPublished: boolean; // ✅ Computed from h_listings + h_listing_publish_status
  // Timestamp fields
  createdAt?: string;
  updatedAt?: string;
  // Arrays
  features: string[];
  platforms: string[]; // Keep as platforms to match database
  images: Array<{
    id: number;
    imageUrl: string;
    isPrimary: boolean;
  }>;
}

export interface VehicleCatalogCreateData {
  partyId: number;
  // Foreign key fields for proper database relationships
  makeId?: number;
  modelId?: number;
  variantId?: number;
  // Text fields for backward compatibility and display
  make: string;
  model: string;
  year: number;
  variant?: string;
  category: string;
  fuelType: string;
  ehailingEligible?: boolean;
  estimatedPrice?: number;
  weeklyFeeTarget?: number;
  weeklyRate?: number;
  initiationFee?: number;
  description?: string;
  specifications?: string;
  features: string[];
  platforms: string[];
  images: Array<{
    imageUrl: string;
    isPrimary: boolean;
  }>;
  isActive?: boolean;
}

export interface VehicleCatalogSearchFilters {
  searchQuery?: string;
  category?: string;
  fuelType?: string;
  ehailingEligible?: boolean;
  make?: string;
  yearFrom?: number;
  yearTo?: number;
}

// ==================== FETCH OPERATIONS ====================

/**
 * Get all vehicle catalog items with their related data
 */
export async function getVehicleCatalogItems(
  filters?: VehicleCatalogSearchFilters
): Promise<VehicleCatalogItem[]> {
  try {
    // Build where conditions
    const whereConditions = [];

    if (filters?.searchQuery) {
      whereConditions.push(
        or(
          like(h_vehicleCatalog.make, `%${filters.searchQuery}%`),
          like(h_vehicleCatalog.model, `%${filters.searchQuery}%`),
          like(h_vehicleCatalog.variant, `%${filters.searchQuery}%`)
        )
      );
    }

    if (filters?.category) {
      whereConditions.push(eq(h_vehicleCatalog.category, filters.category));
    }

    if (filters?.fuelType) {
      whereConditions.push(eq(h_vehicleCatalog.fuelType, filters.fuelType));
    }

    if (filters?.ehailingEligible !== undefined) {
      whereConditions.push(
        eq(h_vehicleCatalog.ehailingEligible, filters.ehailingEligible)
      );
    }

    if (filters?.make) {
      whereConditions.push(eq(h_vehicleCatalog.make, filters.make));
    }

    if (filters?.yearFrom) {
      whereConditions.push(
        sql`${h_vehicleCatalog.year} >= ${filters.yearFrom}`
      );
    }

    if (filters?.yearTo) {
      whereConditions.push(sql`${h_vehicleCatalog.year} <= ${filters.yearTo}`);
    }

    // Get catalog items
    const catalogItems = await db
      .select()
      .from(h_vehicleCatalog)
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)
      .orderBy(desc(h_vehicleCatalog.createdAt));

    // Get publish status for all catalog items
    const catalogIds = catalogItems.map((item) => item.id);
    const publishStatus = await getCatalogItemsPublishStatus(catalogIds);

    // Get related data for each catalog item
    const catalogItemsWithRelations = await Promise.all(
      catalogItems.map(async (item) => {
        const [features, platforms, images] = await Promise.all([
          // Get features
          db
            .select({ feature: h_vehicleCatalogFeatures.feature })
            .from(h_vehicleCatalogFeatures)
            .where(eq(h_vehicleCatalogFeatures.catalogId, item.id)),

          // Get platforms
          db
            .select({ platform: h_vehicleCatalogPlatforms.platform })
            .from(h_vehicleCatalogPlatforms)
            .where(eq(h_vehicleCatalogPlatforms.catalogId, item.id)),

          // Get images
          db
            .select({
              id: h_vehicleCatalogImages.id,
              imageUrl: h_vehicleCatalogImages.imageUrl,
              isPrimary: h_vehicleCatalogImages.isPrimary,
            })
            .from(h_vehicleCatalogImages)
            .where(eq(h_vehicleCatalogImages.catalogId, item.id))
            .orderBy(
              desc(h_vehicleCatalogImages.isPrimary),
              asc(h_vehicleCatalogImages.id)
            ),
        ]);

        return {
          ...item,
          // ✅ Convert null to undefined for foreign keys
          makeId: item.makeId ?? undefined,
          modelId: item.modelId ?? undefined,
          variantId: item.variantId ?? undefined,
          // ✅ Convert null to undefined for other nullable fields
          variant: item.variant ?? undefined,
          estimatedPrice: item.estimatedPrice ?? undefined,
          weeklyFeeTarget: item.weeklyFeeTarget ?? undefined,
          weeklyRate: item.weeklyRate ?? undefined,
          initiationFee: item.initiationFee ?? undefined,
          description: item.description ?? undefined,
          specifications: item.specifications ?? undefined,
          createdAt: item.createdAt ?? undefined,
          updatedAt: item.updatedAt ?? undefined,
          // ✅ Compute publish status from h_listings
          isPublished: publishStatus[item.id] || false,
          features: features.map((f) => f.feature),
          platforms: platforms.map((p) => p.platform),
          images: images,
        };
      })
    );

    return catalogItemsWithRelations;
  } catch (error) {
    console.error("Error fetching vehicle catalog items:", error);
    throw new Error("Failed to fetch vehicle catalog items");
  }
}

/**
 * Get a single vehicle catalog item by ID with all related data
 */
export async function getVehicleCatalogItemById(
  id: number
): Promise<VehicleCatalogItem | null> {
  try {
    const catalogItem = await db
      .select()
      .from(h_vehicleCatalog)
      .where(eq(h_vehicleCatalog.id, id))
      .limit(1);

    if (catalogItem.length === 0) {
      return null;
    }

    const [features, platforms, images] = await Promise.all([
      // Get features
      db
        .select({ feature: h_vehicleCatalogFeatures.feature })
        .from(h_vehicleCatalogFeatures)
        .where(eq(h_vehicleCatalogFeatures.catalogId, id)),

      // Get platforms
      db
        .select({ platform: h_vehicleCatalogPlatforms.platform })
        .from(h_vehicleCatalogPlatforms)
        .where(eq(h_vehicleCatalogPlatforms.catalogId, id)),

      // Get images
      db
        .select({
          id: h_vehicleCatalogImages.id,
          imageUrl: h_vehicleCatalogImages.imageUrl,
          isPrimary: h_vehicleCatalogImages.isPrimary,
        })
        .from(h_vehicleCatalogImages)
        .where(eq(h_vehicleCatalogImages.catalogId, id))
        .orderBy(
          desc(h_vehicleCatalogImages.isPrimary),
          asc(h_vehicleCatalogImages.id)
        ),
    ]);

    // Check if this catalog item is published
    const isPublished = await isCatalogItemPublished(id);

    return {
      ...catalogItem[0],
      // ✅ Convert null to undefined for foreign keys
      makeId: catalogItem[0].makeId ?? undefined,
      modelId: catalogItem[0].modelId ?? undefined,
      variantId: catalogItem[0].variantId ?? undefined,
      // ✅ Convert null to undefined for other nullable fields
      variant: catalogItem[0].variant ?? undefined,
      estimatedPrice: catalogItem[0].estimatedPrice ?? undefined,
      weeklyFeeTarget: catalogItem[0].weeklyFeeTarget ?? undefined,
      weeklyRate: catalogItem[0].weeklyRate ?? undefined,
      initiationFee: catalogItem[0].initiationFee ?? undefined,
      description: catalogItem[0].description ?? undefined,
      specifications: catalogItem[0].specifications ?? undefined,
      createdAt: catalogItem[0].createdAt ?? undefined,
      updatedAt: catalogItem[0].updatedAt ?? undefined,
      // ✅ Compute publish status from h_listings
      isPublished: isPublished,
      features: features.map((f) => f.feature),
      platforms: platforms.map((p) => p.platform),
      images: images,
    };
  } catch (error) {
    console.error("Error fetching vehicle catalog item:", error);
    throw new Error("Failed to fetch vehicle catalog item");
  }
}

// ==================== CREATE OPERATIONS ====================

/**
 * Create a new vehicle catalog item with proper foreign key relationships
 */
export async function createVehicleCatalogItem(
  data: VehicleCatalogCreateData
): Promise<VehicleCatalogItem> {
  try {
    // Validate required foreign key fields
    if (!data.makeId) {
      throw new Error("makeId is required - please select a vehicle make");
    }
    if (!data.modelId) {
      throw new Error("modelId is required - please select a vehicle model");
    }

    console.log("🚀 Creating vehicle catalog item with data:", {
      partyId: data.partyId,
      makeId: data.makeId,
      modelId: data.modelId,
      variantId: data.variantId,
      make: data.make,
      model: data.model,
      year: data.year,
      variant: data.variant,
      category: data.category,
      fuelType: data.fuelType,
      weeklyRate: data.weeklyRate,
      initiationFee: data.initiationFee,
      isActive: data.isActive,
    });

    return await db.transaction(async (tx) => {
      // Create the main catalog item with foreign key references
      const catalogItem = await tx
        .insert(h_vehicleCatalog)
        .values({
          partyId: data.partyId,
          makeId: data.makeId, // Required - validated above
          modelId: data.modelId, // Required - validated above
          variantId: data.variantId || null, // Optional - can be null
          make: data.make,
          model: data.model,
          year: data.year,
          variant: data.variant,
          category: data.category,
          fuelType: data.fuelType,
          ehailingEligible: data.ehailingEligible || false,
          estimatedPrice: data.estimatedPrice,
          weeklyFeeTarget: data.weeklyFeeTarget,
          weeklyRate: data.weeklyRate, // ✅ Now exists in database
          initiationFee: data.initiationFee, // ✅ Now exists in database
          isActive: data.isActive ?? true, // ✅ Now exists in database
          description: data.description,
          specifications: data.specifications,
        })
        .returning();

      const catalogId = catalogItem[0].id;

      // Insert features
      if (data.features.length > 0) {
        await tx.insert(h_vehicleCatalogFeatures).values(
          data.features.map((feature) => ({
            catalogId,
            feature,
          }))
        );
      }

      // Insert platforms
      if (data.platforms.length > 0) {
        await tx.insert(h_vehicleCatalogPlatforms).values(
          data.platforms.map((platform) => ({
            catalogId,
            platform,
          }))
        );
      }

      // Insert images
      console.log(
        "🖼️  Processing images for catalog ID:",
        catalogId,
        "Images:",
        data.images
      );
      if (data.images.length > 0) {
        const imageValues = data.images.map((image) => ({
          catalogId,
          imageUrl: image.imageUrl,
          isPrimary: image.isPrimary,
        }));
        console.log(
          "📥 Inserting images into h_vehicle_catalog_images:",
          imageValues
        );

        const insertedImages = await tx
          .insert(h_vehicleCatalogImages)
          .values(imageValues)
          .returning();
        console.log("✅ Successfully inserted images:", insertedImages);
      } else {
        console.log("ℹ️  No images to insert for catalog ID:", catalogId);
      }

      // Return the complete item
      return {
        ...catalogItem[0],
        // ✅ Convert null to undefined for foreign keys
        makeId: data.makeId ?? undefined,
        modelId: data.modelId ?? undefined,
        variantId: data.variantId ?? undefined,
        // ✅ Convert null to undefined for other nullable fields
        variant: catalogItem[0].variant ?? undefined,
        estimatedPrice: catalogItem[0].estimatedPrice ?? undefined,
        weeklyFeeTarget: catalogItem[0].weeklyFeeTarget ?? undefined,
        weeklyRate: catalogItem[0].weeklyRate ?? undefined,
        initiationFee: catalogItem[0].initiationFee ?? undefined,
        description: catalogItem[0].description ?? undefined,
        specifications: catalogItem[0].specifications ?? undefined,
        createdAt: catalogItem[0].createdAt ?? undefined,
        updatedAt: catalogItem[0].updatedAt ?? undefined,
        // ✅ New items are not published initially
        isPublished: false,
        features: data.features,
        platforms: data.platforms,
        images: data.images.map((img, index) => ({
          id: index + 1, // Temporary ID for new images
          imageUrl: img.imageUrl,
          isPrimary: img.isPrimary,
        })),
      };
    });
  } catch (error) {
    console.error("Error creating vehicle catalog item:", error);
    throw new Error("Failed to create vehicle catalog item");
  }
}

// ==================== UPDATE OPERATIONS ====================

/**
 * Update a vehicle catalog item with all related data
 */
export async function updateVehicleCatalogItem(
  id: number,
  data: Partial<VehicleCatalogCreateData>
): Promise<VehicleCatalogItem | null> {
  try {
    return await db.transaction(async (tx) => {
      // Check if catalog item exists
      const existingItem = await tx
        .select()
        .from(h_vehicleCatalog)
        .where(eq(h_vehicleCatalog.id, id))
        .limit(1);

      if (existingItem.length === 0) {
        return null;
      }

      // Update the main catalog item
      await tx
        .update(h_vehicleCatalog)
        .set({
          partyId: data.partyId,
          make: data.make,
          model: data.model,
          year: data.year,
          variant: data.variant,
          category: data.category,
          fuelType: data.fuelType,
          ehailingEligible: data.ehailingEligible,
          estimatedPrice: data.estimatedPrice,
          weeklyFeeTarget: data.weeklyFeeTarget,
          description: data.description,
          specifications: data.specifications,
          updatedAt: new Date().toISOString(),
        })
        .where(eq(h_vehicleCatalog.id, id));

      // Update features if provided
      if (data.features !== undefined) {
        // Delete existing features
        await tx
          .delete(h_vehicleCatalogFeatures)
          .where(eq(h_vehicleCatalogFeatures.catalogId, id));

        // Insert new features
        if (data.features.length > 0) {
          await tx.insert(h_vehicleCatalogFeatures).values(
            data.features.map((feature) => ({
              catalogId: id,
              feature,
            }))
          );
        }
      }

      // Update platforms if provided
      if (data.platforms !== undefined) {
        // Delete existing platforms
        await tx
          .delete(h_vehicleCatalogPlatforms)
          .where(eq(h_vehicleCatalogPlatforms.catalogId, id));

        // Insert new platforms
        if (data.platforms.length > 0) {
          await tx.insert(h_vehicleCatalogPlatforms).values(
            data.platforms.map((platform) => ({
              catalogId: id,
              platform,
            }))
          );
        }
      }

      // Update images if provided
      if (data.images !== undefined) {
        console.log(
          "🔄 Updating images for catalog ID:",
          id,
          "New images:",
          data.images
        );

        // Delete existing images
        await tx
          .delete(h_vehicleCatalogImages)
          .where(eq(h_vehicleCatalogImages.catalogId, id));
        console.log("🗑️  Deleted existing images for catalog ID:", id);

        // Insert new images
        if (data.images.length > 0) {
          const imageValues = data.images.map((image) => ({
            catalogId: id,
            imageUrl: image.imageUrl,
            isPrimary: image.isPrimary,
          }));
          console.log(
            "📥 Inserting updated images into h_vehicle_catalog_images:",
            imageValues
          );

          const insertedImages = await tx
            .insert(h_vehicleCatalogImages)
            .values(imageValues)
            .returning();
          console.log("✅ Successfully updated images:", insertedImages);
        } else {
          console.log("ℹ️  No new images to insert for catalog ID:", id);
        }
      }

      // Get the updated item with all relations
      return await getVehicleCatalogItemById(id);
    });
  } catch (error) {
    console.error("Error updating vehicle catalog item:", error);
    throw new Error("Failed to update vehicle catalog item");
  }
}

// ==================== DELETE OPERATIONS ====================

/**
 * Delete a vehicle catalog item and all related data
 */
export async function deleteVehicleCatalogItem(id: number): Promise<boolean> {
  try {
    return await db.transaction(async (tx) => {
      // Check if catalog item exists
      const existingItem = await tx
        .select()
        .from(h_vehicleCatalog)
        .where(eq(h_vehicleCatalog.id, id))
        .limit(1);

      if (existingItem.length === 0) {
        return false;
      }

      // Delete related data (cascade should handle this, but being explicit)
      await tx
        .delete(h_vehicleCatalogFeatures)
        .where(eq(h_vehicleCatalogFeatures.catalogId, id));

      await tx
        .delete(h_vehicleCatalogPlatforms)
        .where(eq(h_vehicleCatalogPlatforms.catalogId, id));

      await tx
        .delete(h_vehicleCatalogImages)
        .where(eq(h_vehicleCatalogImages.catalogId, id));

      // Delete the main catalog item
      await tx.delete(h_vehicleCatalog).where(eq(h_vehicleCatalog.id, id));

      return true;
    });
  } catch (error) {
    console.error("Error deleting vehicle catalog item:", error);
    throw new Error("Failed to delete vehicle catalog item");
  }
}

// ==================== PUBLISH STATUS HELPERS ====================

/**
 * Check if a catalog item is published by looking at h_listings and h_listing_publish_status
 */
export async function isCatalogItemPublished(
  catalogId: number
): Promise<boolean> {
  try {
    const publishedListing = await db
      .select({ id: h_listings.id })
      .from(h_listings)
      .leftJoin(
        h_listing_publish_status,
        sql`${h_listings.id} = ${h_listing_publish_status.listingId} AND ${h_listing_publish_status.id} = (
          SELECT id FROM h_listing_publish_status AS lps 
          WHERE lps.listing_id = ${h_listings.id} 
          ORDER BY lps.status_at DESC 
          LIMIT 1
        )`
      )
      .where(
        and(
          eq(h_listings.sourceType, "catalog"),
          eq(h_listings.sourceId, catalogId),
          eq(h_listing_publish_status.status, "published")
        )
      )
      .limit(1);

    return publishedListing.length > 0;
  } catch (error) {
    console.error("Error checking catalog item publish status:", error);
    return false;
  }
}

/**
 * Get publish status for multiple catalog items efficiently
 */
export async function getCatalogItemsPublishStatus(
  catalogIds: number[]
): Promise<{ [key: number]: boolean }> {
  try {
    if (catalogIds.length === 0) return {};

    const publishedListings = await db
      .select({
        catalogId: h_listings.sourceId,
        listingId: h_listings.id,
      })
      .from(h_listings)
      .leftJoin(
        h_listing_publish_status,
        sql`${h_listings.id} = ${h_listing_publish_status.listingId} AND ${h_listing_publish_status.id} = (
          SELECT id FROM h_listing_publish_status AS lps 
          WHERE lps.listing_id = ${h_listings.id} 
          ORDER BY lps.status_at DESC 
          LIMIT 1
        )`
      )
      .where(
        and(
          eq(h_listings.sourceType, "catalog"),
          inArray(h_listings.sourceId, catalogIds),
          eq(h_listing_publish_status.status, "published")
        )
      );

    const publishStatus: { [key: number]: boolean } = {};
    catalogIds.forEach((id) => (publishStatus[id] = false));
    publishedListings.forEach((listing) => {
      if (listing.catalogId) {
        publishStatus[listing.catalogId] = true;
      }
    });

    return publishStatus;
  } catch (error) {
    console.error("Error getting catalog items publish status:", error);
    return {};
  }
}

// ==================== PUBLISH TO LISTINGS ====================

/**
 * Publish a catalog item to h_listings table
 */
export async function publishCatalogItemToListings(
  catalogId: number,
  listingDetails: {
    rate?: number;
    type?: string;
    initiationFee?: number;
    driverExperienceRequired?: number;
    minimumAge?: number;
    preferredGender?: string;
    preferredLocation?: string;
  } = {}
): Promise<{ success: boolean; listingId?: number; error?: string }> {
  try {
    const userAttributes = await getUserAttributes();

    if (!userAttributes) {
      return { success: false, error: "User not authenticated" };
    }

    const partyId = userAttributes["custom:db_id"];

    if (!partyId) {
      return { success: false, error: "User party ID not found" };
    }

    return await db.transaction(async (tx) => {
      // Verify catalog item exists
      const catalogItem = await tx
        .select()
        .from(h_vehicleCatalog)
        .where(eq(h_vehicleCatalog.id, catalogId))
        .limit(1);

      if (catalogItem.length === 0) {
        return { success: false, error: "Catalog item not found" };
      }

      // Check if already published
      const existingListing = await tx
        .select()
        .from(h_listings)
        .where(
          and(
            eq(h_listings.sourceType, "catalog"),
            eq(h_listings.sourceId, catalogId)
          )
        )
        .limit(1);

      if (existingListing.length > 0) {
        return { success: false, error: "Catalog item already published" };
      }

      // Prepare listing details
      const defaultListingDetails = {
        rate: listingDetails.rate || catalogItem[0].weeklyFeeTarget || 2700,
        type: listingDetails.type || "weekly",
        initiationFee: listingDetails.initiationFee || 7500,
        driverExperienceRequired: listingDetails.driverExperienceRequired || 1,
        minimumAge: listingDetails.minimumAge || 21,
        preferredGender: listingDetails.preferredGender || null,
        preferredLocation: listingDetails.preferredLocation || null,
      };

      // Create the listing
      const newListing = await tx
        .insert(h_listings)
        .values({
          partyId: parseInt(partyId),
          sourceType: "catalog",
          sourceId: catalogId,
          listingType: "ehailing-platform",
          effectiveFrom: new Date().toISOString(),
          effectiveTo: new Date(
            Date.now() + 90 * 24 * 60 * 60 * 1000
          ).toISOString(), // 90 days
          listingDetails: JSON.stringify(defaultListingDetails),
        })
        .returning();

      // Create initial approval status (approved since it's admin)
      await tx.insert(h_listing_approval_status).values({
        listingId: newListing[0].id,
        status: "approved",
        statusAt: new Date().toISOString(),
        statusBy: parseInt(partyId),
      });

      // Create initial publish status (published)
      await tx.insert(h_listing_publish_status).values({
        listingId: newListing[0].id,
        status: "published",
        statusAt: new Date().toISOString(),
        statusBy: parseInt(partyId),
      });

      return { success: true, listingId: newListing[0].id };
    });
  } catch (error) {
    console.error("Error publishing catalog item to listings:", error);
    return { success: false, error: "Failed to publish catalog item" };
  }
}

// ==================== PUBLISHED VEHICLES ====================

/**
 * Get published catalog vehicles for public display (e.g., VehicleDiscoveryDrawer)
 * Returns only vehicles that have been published to listings with 'published' status
 */
export async function getPublishedCatalogVehicles(): Promise<
  VehicleCatalogItem[]
> {
  try {
    // Get published listings from catalog
    const publishedListings = await db
      .select({
        catalogId: h_listings.sourceId,
        listingId: h_listings.id,
        listingDetails: h_listings.listingDetails,
        effectiveFrom: h_listings.effectiveFrom,
        effectiveTo: h_listings.effectiveTo,
      })
      .from(h_listings)
      .leftJoin(
        h_listing_publish_status,
        sql`${h_listings.id} = ${h_listing_publish_status.listingId} AND ${h_listing_publish_status.id} = (
          SELECT id FROM h_listing_publish_status AS lps 
          WHERE lps.listing_id = ${h_listings.id} 
          ORDER BY lps.status_at DESC 
          LIMIT 1
        )`
      )
      .where(
        and(
          eq(h_listings.sourceType, "catalog"),
          eq(h_listing_publish_status.status, "published")
        )
      )
      .orderBy(desc(h_listings.effectiveFrom));

    if (publishedListings.length === 0) {
      return [];
    }

    // Get catalog IDs
    const catalogIds = publishedListings.map((listing) => listing.catalogId);

    // Get catalog items with their related data
    const catalogItems = await db
      .select()
      .from(h_vehicleCatalog)
      .where(inArray(h_vehicleCatalog.id, catalogIds))
      .orderBy(desc(h_vehicleCatalog.createdAt));

    // Get related data for each catalog item
    const catalogItemsWithRelations = await Promise.all(
      catalogItems.map(async (item) => {
        const [features, platforms, images] = await Promise.all([
          // Get features
          db
            .select({ feature: h_vehicleCatalogFeatures.feature })
            .from(h_vehicleCatalogFeatures)
            .where(eq(h_vehicleCatalogFeatures.catalogId, item.id)),

          // Get platforms
          db
            .select({ platform: h_vehicleCatalogPlatforms.platform })
            .from(h_vehicleCatalogPlatforms)
            .where(eq(h_vehicleCatalogPlatforms.catalogId, item.id)),

          // Get images
          db
            .select({
              id: h_vehicleCatalogImages.id,
              imageUrl: h_vehicleCatalogImages.imageUrl,
              isPrimary: h_vehicleCatalogImages.isPrimary,
            })
            .from(h_vehicleCatalogImages)
            .where(eq(h_vehicleCatalogImages.catalogId, item.id))
            .orderBy(
              desc(h_vehicleCatalogImages.isPrimary),
              asc(h_vehicleCatalogImages.id)
            ),
        ]);

        return {
          ...item,
          // ✅ Convert null to undefined for foreign keys
          makeId: item.makeId ?? undefined,
          modelId: item.modelId ?? undefined,
          variantId: item.variantId ?? undefined,
          // ✅ Convert null to undefined for other nullable fields
          variant: item.variant ?? undefined,
          estimatedPrice: item.estimatedPrice ?? undefined,
          weeklyFeeTarget: item.weeklyFeeTarget ?? undefined,
          weeklyRate: item.weeklyRate ?? undefined,
          initiationFee: item.initiationFee ?? undefined,
          description: item.description ?? undefined,
          specifications: item.specifications ?? undefined,
          createdAt: item.createdAt ?? undefined,
          updatedAt: item.updatedAt ?? undefined,
          // ✅ All items returned by this function are published
          isPublished: true,
          features: features.map((f) => f.feature),
          platforms: platforms.map((p) => p.platform),
          images: images,
        };
      })
    );

    return catalogItemsWithRelations;
  } catch (error) {
    console.error("Error fetching published catalog vehicles:", error);
    throw new Error("Failed to fetch published catalog vehicles");
  }
}

// ==================== UTILITY FUNCTIONS ====================

/**
 * Get unique values for filtering
 */
export async function getVehicleCatalogFilterOptions(): Promise<{
  categories: string[];
  fuelTypes: string[];
  makes: string[];
  yearRange: { min: number; max: number };
}> {
  try {
    const [categories, fuelTypes, makes, yearRange] = await Promise.all([
      // Get unique categories
      db
        .selectDistinct({ category: h_vehicleCatalog.category })
        .from(h_vehicleCatalog)
        .orderBy(asc(h_vehicleCatalog.category)),

      // Get unique fuel types
      db
        .selectDistinct({ fuelType: h_vehicleCatalog.fuelType })
        .from(h_vehicleCatalog)
        .orderBy(asc(h_vehicleCatalog.fuelType)),

      // Get unique makes
      db
        .selectDistinct({ make: h_vehicleCatalog.make })
        .from(h_vehicleCatalog)
        .orderBy(asc(h_vehicleCatalog.make)),

      // Get year range
      db
        .select({
          min: sql<number>`MIN(${h_vehicleCatalog.year})`,
          max: sql<number>`MAX(${h_vehicleCatalog.year})`,
        })
        .from(h_vehicleCatalog),
    ]);

    return {
      categories: categories.map((c) => c.category),
      fuelTypes: fuelTypes.map((f) => f.fuelType),
      makes: makes.map((m) => m.make),
      yearRange: yearRange[0] || { min: 2020, max: new Date().getFullYear() },
    };
  } catch (error) {
    console.error("Error fetching filter options:", error);
    throw new Error("Failed to fetch filter options");
  }
}
