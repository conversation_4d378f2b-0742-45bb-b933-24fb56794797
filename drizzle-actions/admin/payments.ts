"use server";

import { db } from "@/db";
import { eq, desc, and, sql } from "drizzle-orm";
import { getCurrentUserNoCache } from "@/lib/amplifyServerUtils";
import {
  h_assignment_deposits,
  h_driver_payouts,
  h_driver_debt_tracking,
} from "@/drizzle/h_schema/payment-contract";
import {
  vehicles,
  party,
  individual,
  vehicleModel,
  vehicleMake,
} from "@/drizzle/schema";
import {
  h_assignments,
  h_assignment_initiation_fee, // ✅ Add initiation fee table
} from "@/drizzle/h_schema/assignments";

// Types for payment records
export interface PaymentRecord {
  id: string;
  assignmentId: string;
  driverName: string;
  vehicleName: string;
  vehicleRegistration: string;
  paymentType: "weekly_lease" | "deposit" | "payout" | "debt_recovery" | "initiation_fee"; // ✅ Added initiation_fee
  amount: number;
  dueDate: string;
  paidDate?: string;
  status: "paid" | "pending" | "overdue" | "failed";
  paymentMethod?: "bank_transfer" | "cash" | "eft" | "card" | "other";
  reference?: string;
  notes?: string;
}

export interface BasicAssignment {
  id: string;
  driverName: string;
  vehicleName: string;
  vehicleRegistration: string;
  weeklyRate: number;
  outstandingBalance: number;
}

/**
 * Get all payment records from various payment-related tables
 */
export async function getAllPaymentRecords(): Promise<{
  success: boolean;
  data?: PaymentRecord[];
  error?: string;
}> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      return {
        success: false,
        error: "Admin authentication required",
      };
    }

    // Get admin's assignment IDs first
    const adminAssignmentIds = await db
      .select({ id: h_assignments.id })
      .from(h_assignments)
      .where(eq(h_assignments.adminPartyId, adminPartyId));

    const assignmentIds = adminAssignmentIds.map(a => a.id);

    if (assignmentIds.length === 0) {
      console.log(`🔍 [getAllPaymentRecords] No assignments found for admin ${adminPartyId}`);
      return {
        success: true,
        data: [],
      };
    }

    console.log(`🔍 [getAllPaymentRecords] Found ${assignmentIds.length} assignments for admin ${adminPartyId}`);

    // Get payment records filtered by admin's assignments
    const [initiationFees, payouts, debts] = await Promise.all([
      // Get initiation fee payments - only for admin's assignments
      db
        .select({
          id: h_assignment_initiation_fee.id,
          assignmentId: h_assignment_initiation_fee.assignmentId,
          amount: h_assignment_initiation_fee.paidAmount,
          dueDate: h_assignment_initiation_fee.dueDate,
          paidDate: h_assignment_initiation_fee.paymentDate,
          paymentMethod: h_assignment_initiation_fee.paymentMethod,
          reference: h_assignment_initiation_fee.paymentReference,
          notes: h_assignment_initiation_fee.notes,
          type: sql<string>`'initiation_fee'`,
        })
        .from(h_assignment_initiation_fee)
        .where(sql`${h_assignment_initiation_fee.assignmentId} = ANY(${assignmentIds})`)
        .orderBy(desc(h_assignment_initiation_fee.paymentDate)),

      // Get driver payouts - only for admin's assignments
      db
        .select({
          id: h_driver_payouts.id,
          assignmentId: h_driver_payouts.assignmentId,
          amount: h_driver_payouts.netPayout,
          dueDate: h_driver_payouts.paymentDate, // ✅ Keep as dueDate
          paidDate: h_driver_payouts.paymentDate, // ✅ Same date for payouts
          paymentMethod: h_driver_payouts.paymentMethod,
          reference: h_driver_payouts.paymentReference,
          notes: h_driver_payouts.notes,
          type: sql<string>`'payout'`,
        })
        .from(h_driver_payouts)
        .where(sql`${h_driver_payouts.assignmentId} = ANY(${assignmentIds})`)
        .orderBy(desc(h_driver_payouts.paymentDate)),

      // Get debt records - only for admin's assignments
      db
        .select({
          id: h_driver_debt_tracking.id,
          assignmentId: h_driver_debt_tracking.assignmentId,
          amount: h_driver_debt_tracking.debtAmount,
          dueDate: h_driver_debt_tracking.transactionDate,
          paidDate: sql<Date | null>`null`,
          paymentMethod: sql<string | null>`null`,
          reference: sql<string | null>`null`,
          notes: h_driver_debt_tracking.notes,
          type: sql<string>`'debt_recovery'`,
        })
        .from(h_driver_debt_tracking)
        .where(sql`${h_driver_debt_tracking.assignmentId} = ANY(${assignmentIds})`)
        .orderBy(desc(h_driver_debt_tracking.transactionDate)),
    ]);

    // Combine all payment records
    const allPayments = [
      ...initiationFees.map(d => ({
        ...d,
        paymentType: "initiation_fee" as const,
        status: d.paidDate ? ("paid" as const) : ("pending" as const),
      })),
      ...payouts.map(p => ({
        ...p,
        paymentType: "payout" as const,
        status: p.paidDate ? ("paid" as const) : ("pending" as const),
      })),
      ...debts.map(d => ({
        ...d,
        paymentType: "debt_recovery" as const,
        status: "pending" as const,
      })),
    ];

    // Get assignment details for each payment
    const paymentAssignmentIds = [...new Set(allPayments.map(p => p.assignmentId))];
    console.log(`🔍 [getAllPaymentRecords] Found ${allPayments.length} payments with assignment IDs:`, paymentAssignmentIds);

    const assignmentDetails = await getAssignmentDetails(paymentAssignmentIds);
    console.log(`🔍 [getAllPaymentRecords] Got ${assignmentDetails.length} assignment details`);

    // Also get all assignments to see what's available
    const allAssignments = await getAssignmentDetails([]);
    console.log(`🔍 [getAllPaymentRecords] Total assignments available:`, allAssignments.length);

    // Format payment records with assignment details
    const formattedPayments: PaymentRecord[] = allPayments.map(payment => {
      const assignment = assignmentDetails.find(a => a.id === payment.assignmentId.toString());
      
      return {
        id: `${payment.type}-${payment.id}`,
        assignmentId: payment.assignmentId.toString(),
        driverName: assignment?.driverName || "Unknown Driver",
        vehicleName: assignment?.vehicleName || "Unknown Vehicle",
        vehicleRegistration: assignment?.vehicleRegistration || "Unknown",
        paymentType: payment.paymentType,
        amount: Number(payment.amount),
        dueDate: payment.dueDate?.toISOString().split('T')[0] || new Date().toISOString().split('T')[0],
        paidDate: payment.paidDate?.toISOString().split('T')[0],
        status: payment.status,
        paymentMethod: payment.paymentMethod as "bank_transfer" | "cash" | "eft" | "card" | "other" | undefined,
        reference: payment.reference || undefined,
        notes: payment.notes || undefined,
      };
    });

    return {
      success: true,
      data: formattedPayments,
    };
  } catch (error) {
    console.error("❌ [getAllPaymentRecords] Error:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to fetch payment records",
    };
  }
}

/**
 * Get assignment details for given assignment IDs
 * Uses the new h_assignments table for clean, simple queries
 */
async function getAssignmentDetails(assignmentIds: number[]): Promise<BasicAssignment[]> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      return [];
    }

    console.log(`🔍 [getAssignmentDetails] Looking for assignments for admin ${adminPartyId}`);

    // Build the query conditions
    let whereConditions = [eq(h_assignments.adminPartyId, adminPartyId)];

    // Only filter by assignment IDs if they are provided
    if (assignmentIds.length > 0) {
      whereConditions.push(sql`${h_assignments.id} = ANY(${assignmentIds})`);
    }

    // Get assignments with full details (clean joins)
    const assignments = await db
      .select({
        id: h_assignments.id,
        driverPartyId: h_assignments.driverPartyId,
        vehicleId: h_assignments.vehicleId,
        status: h_assignments.status,
        createdAt: h_assignments.createdAt,

        // Driver details
        driverFirstName: individual.firstName,
        driverLastName: individual.lastName,

        // Vehicle details
        vehicleMake: vehicleMake.name,
        vehicleModel: vehicleModel.model,
        vehicleYear: vehicles.manufacturingYear,
        vehicleRegistration: vehicles.vehicleRegistration,
      })
      .from(h_assignments)
      .innerJoin(party, eq(party.id, h_assignments.driverPartyId))
      .leftJoin(individual, eq(individual.partyId, party.id))
      .innerJoin(vehicles, eq(vehicles.id, h_assignments.vehicleId))
      .leftJoin(vehicleModel, eq(vehicleModel.id, vehicles.modelId))
      .leftJoin(vehicleMake, eq(vehicleMake.id, vehicleModel.makeId))
      .where(and(...whereConditions))
      .orderBy(desc(h_assignments.createdAt));

    console.log(`🔍 [getAssignmentDetails] Found ${assignments.length} assignments for admin ${adminPartyId}`);

    // Transform to BasicAssignment format
    const transformedAssignments: BasicAssignment[] = assignments.map(assignment => {
      return {
        id: assignment.id.toString(),
        driverName: `${assignment.driverFirstName || ""} ${assignment.driverLastName || ""}`.trim() || "Unknown Driver",
        vehicleName: `${assignment.vehicleMake || "Unknown"} ${assignment.vehicleModel || "Unknown"} ${assignment.vehicleYear || ""}`.trim(),
        vehicleRegistration: assignment.vehicleRegistration || "Unknown",
        weeklyRate: 2800, // Default - will be joined from payment tables later
        outstandingBalance: 0, // Will be calculated from payment tables later
      };
    });

    console.log(`🔍 [getAssignmentDetails] Transformed ${transformedAssignments.length} assignments:`, transformedAssignments);

    return transformedAssignments;
  } catch (error) {
    console.error("❌ [getAssignmentDetails] Error:", error);
    return [];
  }
}

/**
 * Get basic assignment information for dropdowns and forms
 * This will return all active assignments for the current admin
 */
export async function getBasicAssignments(): Promise<{
  success: boolean;
  data?: BasicAssignment[];
  error?: string;
}> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      return {
        success: false,
        error: "Admin authentication required",
      };
    }

    // Get all assignments (don't filter by specific IDs)
    const assignments = await getAssignmentDetails([]);

    return {
      success: true,
      data: assignments,
    };
  } catch (error) {
    console.error("❌ [getBasicAssignments] Error:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to fetch assignments",
    };
  }
}
