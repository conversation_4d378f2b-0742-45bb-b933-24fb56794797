"use server";

import { db } from "@/db";
import { eq, desc, and, gte, lte, sum, sql, ne, isNull } from "drizzle-orm";
import { getCurrentUserNoCache } from "@/lib/amplifyServerUtils";
import {
  h_driver_payouts,
  h_driver_debt_tracking,
  h_weekly_earnings,
  h_payment_audit_log,
} from "@/drizzle/h_schema/payment-contract";
import { party } from "@/drizzle/schema";
import {
  PayoutRecord,
  PayoutProcessingInput,
  DebtRecord,
  PaymentError,
  PayoutSummary,
  PayoutCalculation,
} from "@/types/payment-contract";

// =====================================================
// PAYOUT CALCULATION AND PROCESSING
// =====================================================

/**
 * Calculate payout amount for an assignment based on earnings and debts
 */
export async function calculatePayoutAmount(
  assignmentId: number,
  startDate: string,
  endDate: string
): Promise<{
  success: boolean;
  data?: PayoutCalculation;
  error?: PaymentError;
}> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      return {
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Admin authentication required",
        },
      };
    }

    // Get total gross earnings for the period
    const earningsQuery = await db
      .select({
        totalGrossEarnings: sum(h_weekly_earnings.grossEarnings),
        totalWeeklyTargets: sum(h_weekly_earnings.weeklyTarget),
        totalShortfall: sum(h_weekly_earnings.earningsShortfall),
        weeksCount: sql<number>`COUNT(*)`,
      })
      .from(h_weekly_earnings)
      .where(
        and(
          eq(h_weekly_earnings.assignmentId, assignmentId),
          gte(h_weekly_earnings.weekStartDate, startDate),
          lte(h_weekly_earnings.weekEndDate, endDate)
        )
      );

    const earnings = earningsQuery[0];
    const totalGrossEarnings = Number(earnings.totalGrossEarnings) || 0;
    const totalWeeklyTargets = Number(earnings.totalWeeklyTargets) || 0;
    const totalShortfall = Number(earnings.totalShortfall) || 0;
    const weeksCount = Number(earnings.weeksCount) || 0;

    // For now, assume net earnings = gross earnings (no expense tracking in current schema)
    const totalNetEarnings = totalGrossEarnings;
    const totalExpenses = 0; // No expense tracking in current schema

    // Get outstanding debts for this assignment (using runningBalance as the current debt amount)
    const debtsQuery = await db
      .select({
        totalDebt: sum(h_driver_debt_tracking.runningBalance),
        debtCount: sql<number>`COUNT(*)`,
      })
      .from(h_driver_debt_tracking)
      .where(
        and(
          eq(h_driver_debt_tracking.assignmentId, assignmentId),
          sql`${h_driver_debt_tracking.runningBalance} > 0` // Only count debts with positive balance
        )
      );

    const debts = debtsQuery[0];
    const totalOutstandingDebt = Number(debts.totalDebt) || 0;
    const debtCount = Number(debts.debtCount) || 0;

    // Calculate final payout amount (net earnings minus outstanding debts)
    const finalPayoutAmount = Math.max(
      0,
      totalNetEarnings - totalOutstandingDebt
    );
    const debtDeduction = Math.min(totalNetEarnings, totalOutstandingDebt);

    const calculation: PayoutCalculation = {
      assignmentId,
      periodStart: startDate,
      periodEnd: endDate,
      totalGrossEarnings: totalGrossEarnings.toString(),
      totalExpenses: totalExpenses.toString(),
      totalNetEarnings: totalNetEarnings.toString(),
      totalOutstandingDebt: totalOutstandingDebt.toString(),
      debtDeduction: debtDeduction.toString(),
      finalPayoutAmount: finalPayoutAmount.toString(),
      weeksIncluded: weeksCount,
      debtsIncluded: debtCount,
      averageWeeklyNet:
        weeksCount > 0 ? (totalNetEarnings / weeksCount).toString() : "0",
    };

    return {
      success: true,
      data: calculation,
    };
  } catch (error) {
    console.error("❌ [calculatePayoutAmount] Error:", error);
    return {
      success: false,
      error: {
        code: "PAYOUT_CALCULATION_FAILED",
        message:
          error instanceof Error
            ? error.message
            : "Failed to calculate payout amount",
      },
    };
  }
}

/**
 * Process a driver payout
 */
export async function processDriverPayout(
  input: PayoutProcessingInput
): Promise<{
  success: boolean;
  data?: PayoutRecord;
  debtsResolved?: DebtRecord[];
  error?: PaymentError;
}> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      return {
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Admin authentication required",
        },
      };
    }

    // Validate dates
    const periodStart = new Date(input.periodStart);
    const periodEnd = new Date(input.periodEnd);

    if (periodEnd <= periodStart) {
      return {
        success: false,
        error: {
          code: "INVALID_DATE_RANGE",
          message: "Period end date must be after start date",
          field: "periodEnd",
        },
      };
    }

    // Get payout calculation to validate amounts
    const calculationResult = await calculatePayoutAmount(
      input.assignmentId,
      input.periodStart,
      input.periodEnd
    );

    if (!calculationResult.success || !calculationResult.data) {
      return {
        success: false,
        error: {
          code: "CALCULATION_FAILED",
          message: "Failed to calculate payout amount",
        },
      };
    }

    const calculation = calculationResult.data;
    const expectedAmount = Number(calculation.finalPayoutAmount);

    // Validate payout amount doesn't exceed calculated amount
    if (input.payoutAmount > expectedAmount) {
      return {
        success: false,
        error: {
          code: "INVALID_PAYOUT_AMOUNT",
          message: `Payout amount (R${input.payoutAmount}) exceeds calculated amount (R${expectedAmount})`,
          field: "payoutAmount",
        },
      };
    }

    // Start transaction
    const result = await db.transaction(async (tx) => {
      // Note: The current schema expects weeklyEarningsId, but we're implementing period-based payouts
      // This is a schema mismatch that needs to be resolved. For now, we'll use a placeholder.
      // TODO: Update schema to support period-based payouts or change implementation to be weekly-based

      // Get the first weekly earnings record in the period as a placeholder
      const firstEarningsRecord = await tx
        .select()
        .from(h_weekly_earnings)
        .where(
          and(
            eq(h_weekly_earnings.assignmentId, input.assignmentId),
            gte(h_weekly_earnings.weekStartDate, input.periodStart),
            lte(h_weekly_earnings.weekEndDate, input.periodEnd)
          )
        )
        .limit(1);

      if (firstEarningsRecord.length === 0) {
        throw new Error("No earnings records found for the specified period");
      }

      // Insert payout record using the schema-expected fields
      const [payoutRecord] = await tx
        .insert(h_driver_payouts)
        .values({
          assignmentId: input.assignmentId,
          weeklyEarningsId: firstEarningsRecord[0].id, // Required by schema
          grossPayout: (
            input.payoutAmount + Number(calculation.debtDeduction)
          ).toString(),
          debtDeduction: calculation.debtDeduction,
          netPayout: input.payoutAmount.toString(),
          paymentMethod: input.paymentMethod,
          paymentReference: input.paymentReference,
          paymentDate: new Date(),
          processedBy: adminPartyId,
          status: input.requiresApproval ? "pending" : "paid",
          notes: input.notes,
        })
        .returning();

      // Resolve debts with deduction amount
      const resolvedDebts: DebtRecord[] = [];
      let remainingDeduction = Number(calculation.debtDeduction);

      if (remainingDeduction > 0) {
        // Get outstanding debts in order (oldest first)
        const outstandingDebts = await tx
          .select()
          .from(h_driver_debt_tracking)
          .where(
            and(
              eq(h_driver_debt_tracking.assignmentId, input.assignmentId),
              sql`${h_driver_debt_tracking.runningBalance} > 0`
            )
          )
          .orderBy(h_driver_debt_tracking.transactionDate);

        for (const debt of outstandingDebts) {
          if (remainingDeduction <= 0) break;

          const debtAmount = Number(debt.runningBalance);
          const deductionAmount = Math.min(debtAmount, remainingDeduction);
          const newBalance = debtAmount - deductionAmount;

          // Update the debt balance (set to 0 if fully paid)
          await tx
            .update(h_driver_debt_tracking)
            .set({
              runningBalance: newBalance.toString(),
              notes: debt.notes
                ? `${debt.notes}\nPartial payment via payout deduction (Payout ID: ${payoutRecord.id}): R${deductionAmount}`
                : `Partial payment via payout deduction (Payout ID: ${payoutRecord.id}): R${deductionAmount}`,
            })
            .where(eq(h_driver_debt_tracking.id, debt.id));

          resolvedDebts.push({
            id: debt.id,
            assignmentId: debt.assignmentId,
            debtSourceId: debt.debtSourceId,
            debtSourceType: debt.debtSourceType,
            debtAmount: Number(debt.debtAmount),
            runningBalance: newBalance,
            transactionDate: debt.transactionDate.toISOString(),
            createdBy: 0, // Default value since createdBy doesn't exist in current schema
            notes: debt.notes || undefined,
          });

          remainingDeduction -= deductionAmount;
        }
      }

      // Create audit log entry
      await tx.insert(h_payment_audit_log).values({
        assignmentId: input.assignmentId,
        actionType: "PROCESS_PAYOUT",
        tableName: "h_driver_payouts",
        recordId: payoutRecord.id,
        newValues: {
          periodStart: input.periodStart,
          periodEnd: input.periodEnd,
          payoutAmount: input.payoutAmount,
          paymentMethod: input.paymentMethod,
          paymentReference: input.paymentReference,
          debtsResolved: resolvedDebts.length,
          totalDebtDeduction: Number(calculation.debtDeduction),
          status: payoutRecord.status,
        },
        performedBy: adminPartyId,
      });

      const formattedPayout: PayoutRecord = {
        id: payoutRecord.id,
        assignmentId: payoutRecord.assignmentId,
        periodStart: input.periodStart, // Store the period info in the response
        periodEnd: input.periodEnd,
        grossEarnings: payoutRecord.grossPayout.toString(),
        totalExpenses: "0", // Not tracked in current schema
        netEarnings: payoutRecord.netPayout.toString(),
        totalDebtDeduction: payoutRecord.debtDeduction?.toString() || "0",
        payoutAmount: payoutRecord.netPayout.toString(),
        paymentMethod: payoutRecord.paymentMethod || undefined,
        paymentReference: payoutRecord.paymentReference || undefined,
        bankDetails: undefined, // Not in current schema
        status: mapDatabaseStatusToTypeScript(payoutRecord.status),
        processedAt: new Date().toISOString(), // Current timestamp since processedAt doesn't exist in schema
        processedBy: payoutRecord.processedBy,
        approvedAt: undefined, // Not in current schema
        approvedBy: undefined, // Not in current schema
        notes: payoutRecord.notes || undefined,
      };

      return { payoutRecord: formattedPayout, resolvedDebts };
    });

    return {
      success: true,
      data: result.payoutRecord,
      debtsResolved: result.resolvedDebts,
    };
  } catch (error) {
    console.error("❌ [processDriverPayout] Error:", error);
    return {
      success: false,
      error: {
        code: "PAYOUT_PROCESSING_FAILED",
        message:
          error instanceof Error
            ? error.message
            : "Failed to process driver payout",
      },
    };
  }
}

/**
 * Approve a pending payout
 */
export async function approveDriverPayout(
  payoutId: number,
  approvalNotes?: string
): Promise<{ success: boolean; data?: PayoutRecord; error?: PaymentError }> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      return {
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Admin authentication required",
        },
      };
    }

    // Get existing payout
    const existingPayouts = await db
      .select()
      .from(h_driver_payouts)
      .where(eq(h_driver_payouts.id, payoutId))
      .limit(1);

    if (existingPayouts.length === 0) {
      return {
        success: false,
        error: {
          code: "PAYOUT_NOT_FOUND",
          message: "Payout record not found",
        },
      };
    }

    const existingPayout = existingPayouts[0];

    if (existingPayout.status !== "pending") {
      return {
        success: false,
        error: {
          code: "INVALID_PAYOUT_STATUS",
          message: `Cannot approve payout with status: ${existingPayout.status}`,
        },
      };
    }

    // Update payout status
    const [updatedPayout] = await db
      .update(h_driver_payouts)
      .set({
        status: "paid",
        paymentDate: new Date(),
        notes: approvalNotes
          ? existingPayout.notes
            ? `${existingPayout.notes}\n\nApproval Notes: ${approvalNotes}`
            : `Approval Notes: ${approvalNotes}`
          : existingPayout.notes,
      })
      .where(eq(h_driver_payouts.id, payoutId))
      .returning();

    // Create audit log entry
    await db.insert(h_payment_audit_log).values({
      assignmentId: existingPayout.assignmentId,
      actionType: "APPROVE_PAYOUT",
      tableName: "h_driver_payouts",
      recordId: payoutId,
      oldValues: {
        status: existingPayout.status,
      },
      newValues: {
        status: "approved",
        approvedBy: adminPartyId,
        approvalNotes,
      },
      performedBy: adminPartyId,
    });

    const formattedPayout: PayoutRecord = {
      id: updatedPayout.id,
      assignmentId: updatedPayout.assignmentId,
      periodStart: "N/A", // Not available in current schema
      periodEnd: "N/A", // Not available in current schema
      grossEarnings: updatedPayout.grossPayout.toString(),
      totalExpenses: "0", // Not tracked in current schema
      netEarnings: updatedPayout.netPayout.toString(),
      totalDebtDeduction: updatedPayout.debtDeduction?.toString() || "0",
      payoutAmount: updatedPayout.netPayout.toString(),
      paymentMethod: updatedPayout.paymentMethod || undefined,
      paymentReference: updatedPayout.paymentReference || undefined,
      bankDetails: undefined, // Not in current schema
      status: mapDatabaseStatusToTypeScript(updatedPayout.status),
      processedAt: new Date().toISOString(), // Current timestamp
      processedBy: updatedPayout.processedBy,
      approvedAt: updatedPayout.paymentDate?.toISOString() || undefined,
      approvedBy: adminPartyId, // The person who approved it
      notes: updatedPayout.notes || undefined,
    };

    return {
      success: true,
      data: formattedPayout,
    };
  } catch (error) {
    console.error("❌ [approveDriverPayout] Error:", error);
    return {
      success: false,
      error: {
        code: "PAYOUT_APPROVAL_FAILED",
        message:
          error instanceof Error
            ? error.message
            : "Failed to approve driver payout",
      },
    };
  }
}

/**
 * Reject a pending payout
 */
export async function rejectDriverPayout(
  payoutId: number,
  rejectionReason: string
): Promise<{ success: boolean; error?: PaymentError }> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      return {
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Admin authentication required",
        },
      };
    }

    // Get existing payout
    const existingPayouts = await db
      .select()
      .from(h_driver_payouts)
      .where(eq(h_driver_payouts.id, payoutId))
      .limit(1);

    if (existingPayouts.length === 0) {
      return {
        success: false,
        error: {
          code: "PAYOUT_NOT_FOUND",
          message: "Payout record not found",
        },
      };
    }

    const existingPayout = existingPayouts[0];

    if (existingPayout.status !== "pending") {
      return {
        success: false,
        error: {
          code: "INVALID_PAYOUT_STATUS",
          message: `Cannot reject payout with status: ${existingPayout.status}`,
        },
      };
    }

    // Start transaction to reject payout and restore debts
    await db.transaction(async (tx) => {
      // Update payout status
      await tx
        .update(h_driver_payouts)
        .set({
          status: "cancelled",
          notes: existingPayout.notes
            ? `${existingPayout.notes}\n\nRejection Reason: ${rejectionReason}`
            : `Rejection Reason: ${rejectionReason}`,
        })
        .where(eq(h_driver_payouts.id, payoutId));

      // Restore any debts that were resolved by this payout
      // Note: This is a simplified approach - in practice, you might want more sophisticated debt restoration
      const debtDeduction = Number(existingPayout.debtDeduction) || 0;

      if (debtDeduction > 0) {
        // Find debts that were affected by this payout and restore them
        // This is a simplified approach - in practice, you'd need more sophisticated debt restoration
        // For now, we'll add the deduction amount back to the running balances
        const affectedDebts = await tx
          .select()
          .from(h_driver_debt_tracking)
          .where(
            and(
              eq(
                h_driver_debt_tracking.assignmentId,
                existingPayout.assignmentId
              ),
              sql`${h_driver_debt_tracking.notes} LIKE '%Payout ID: ${payoutId}%'`
            )
          );

        // This is a simplified restoration - in practice you'd need to track the exact amounts deducted
        for (const debt of affectedDebts) {
          await tx
            .update(h_driver_debt_tracking)
            .set({
              notes: debt.notes
                ? `${debt.notes}\nPayout rejected - debt restoration may be incomplete`
                : `Payout rejected - debt restoration may be incomplete`,
            })
            .where(eq(h_driver_debt_tracking.id, debt.id));
        }
      }

      // Create audit log entry
      await tx.insert(h_payment_audit_log).values({
        assignmentId: existingPayout.assignmentId,
        actionType: "REJECT_PAYOUT",
        tableName: "h_driver_payouts",
        recordId: payoutId,
        oldValues: {
          status: existingPayout.status,
        },
        newValues: {
          status: "cancelled",
          rejectionReason,
          debtsRestored: debtDeduction > 0,
        },
        performedBy: adminPartyId,
      });
    });

    return {
      success: true,
    };
  } catch (error) {
    console.error("❌ [rejectDriverPayout] Error:", error);
    return {
      success: false,
      error: {
        code: "PAYOUT_REJECTION_FAILED",
        message:
          error instanceof Error
            ? error.message
            : "Failed to reject driver payout",
      },
    };
  }
}

// =====================================================
// PAYOUT RETRIEVAL AND SUMMARIES
// =====================================================

/**
 * Get payouts for a specific assignment
 */
export async function getAssignmentPayouts(
  assignmentId: number,
  startDate?: string,
  endDate?: string
): Promise<{ success: boolean; data?: PayoutRecord[]; error?: PaymentError }> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      return {
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Admin authentication required",
        },
      };
    }

    // Build query conditions - note: current schema doesn't support period filtering directly
    const conditions = [eq(h_driver_payouts.assignmentId, assignmentId)];

    // TODO: Add period filtering when schema supports it
    // For now, we can only filter by assignment

    const payouts = await db
      .select()
      .from(h_driver_payouts)
      .where(and(...conditions))
      .orderBy(desc(h_driver_payouts.id)); // Order by ID since processedAt doesn't exist

    const formattedPayouts: PayoutRecord[] = payouts.map((payout) => ({
      id: payout.id,
      assignmentId: payout.assignmentId,
      periodStart: "N/A", // Not available in current schema
      periodEnd: "N/A", // Not available in current schema
      grossEarnings: payout.grossPayout.toString(),
      totalExpenses: "0", // Not tracked in current schema
      netEarnings: payout.netPayout.toString(),
      totalDebtDeduction: payout.debtDeduction?.toString() || "0",
      payoutAmount: payout.netPayout.toString(),
      paymentMethod: payout.paymentMethod || undefined,
      paymentReference: payout.paymentReference || undefined,
      bankDetails: undefined, // Not in current schema
      status: mapDatabaseStatusToTypeScript(payout.status),
      processedAt: new Date().toISOString(), // Placeholder since processedAt doesn't exist
      processedBy: payout.processedBy,
      approvedAt: payout.paymentDate?.toISOString() || undefined,
      approvedBy: undefined, // Not tracked in current schema
      notes: payout.notes || undefined,
    }));

    return {
      success: true,
      data: formattedPayouts,
    };
  } catch (error) {
    console.error("❌ [getAssignmentPayouts] Error:", error);
    return {
      success: false,
      error: {
        code: "FETCH_PAYOUTS_FAILED",
        message:
          error instanceof Error
            ? error.message
            : "Failed to fetch assignment payouts",
      },
    };
  }
}

/**
 * Get payout summaries for multiple assignments
 */
export async function getPayoutsSummary(
  assignmentIds: number[],
  startDate?: string,
  endDate?: string
): Promise<{ success: boolean; data?: PayoutSummary[]; error?: PaymentError }> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      return {
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Admin authentication required",
        },
      };
    }

    const summaries = await Promise.all(
      assignmentIds.map(async (assignmentId) => {
        // Build query conditions - note: current schema doesn't support period filtering directly
        const conditions = [eq(h_driver_payouts.assignmentId, assignmentId)];

        // TODO: Add period filtering when schema supports it

        // Get payout summary
        const summaryQuery = await db
          .select({
            totalPayouts: sum(h_driver_payouts.netPayout),
            totalGrossEarnings: sum(h_driver_payouts.grossPayout),
            totalNetEarnings: sum(h_driver_payouts.netPayout),
            totalDebtDeductions: sum(h_driver_payouts.debtDeduction),
            payoutCount: sql<number>`COUNT(*)`,
            pendingApprovalCount: sql<number>`COUNT(*) FILTER (WHERE status = 'pending')`,
            approvedCount: sql<number>`COUNT(*) FILTER (WHERE status = 'paid')`,
            rejectedCount: sql<number>`COUNT(*) FILTER (WHERE status = 'cancelled')`,
          })
          .from(h_driver_payouts)
          .where(and(...conditions));

        const summary = summaryQuery[0];

        // Get latest payout
        const latestPayout = await db
          .select()
          .from(h_driver_payouts)
          .where(eq(h_driver_payouts.assignmentId, assignmentId))
          .orderBy(desc(h_driver_payouts.id)) // Order by ID since processedAt doesn't exist
          .limit(1);

        return {
          assignmentId,
          totalPayouts: summary.totalPayouts?.toString() || "0",
          totalGrossEarnings: summary.totalGrossEarnings?.toString() || "0",
          totalNetEarnings: summary.totalNetEarnings?.toString() || "0",
          totalDebtDeductions: summary.totalDebtDeductions?.toString() || "0",
          payoutCount: Number(summary.payoutCount) || 0,
          pendingApprovalCount: Number(summary.pendingApprovalCount) || 0,
          approvedCount: Number(summary.approvedCount) || 0,
          rejectedCount: Number(summary.rejectedCount) || 0,
          averagePayoutAmount:
            summary.payoutCount > 0
              ? (
                  Number(summary.totalPayouts) / Number(summary.payoutCount)
                ).toString()
              : "0",
          lastPayoutDate:
            latestPayout[0]?.paymentDate?.toISOString() || undefined,
          lastPayoutAmount: latestPayout[0]?.netPayout.toString() || undefined,
        };
      })
    );

    return {
      success: true,
      data: summaries,
    };
  } catch (error) {
    console.error("❌ [getPayoutsSummary] Error:", error);
    return {
      success: false,
      error: {
        code: "FETCH_PAYOUTS_SUMMARY_FAILED",
        message:
          error instanceof Error
            ? error.message
            : "Failed to fetch payouts summary",
      },
    };
  }
}

// =====================================================
// VALIDATION HELPERS
// =====================================================

/**
 * Map database status values to TypeScript enum values
 */
function mapDatabaseStatusToTypeScript(
  dbStatus: "pending" | "paid" | "failed" | "cancelled"
): "pending_approval" | "approved" | "rejected" | "processed" {
  switch (dbStatus) {
    case "pending":
      return "pending_approval";
    case "paid":
      return "approved";
    case "failed":
      return "rejected";
    case "cancelled":
      return "rejected";
    default:
      return "processed";
  }
}

/**
 * Validate payout processing input
 */
export async function validatePayoutProcessing(input: PayoutProcessingInput): Promise<{
  isValid: boolean;
  errors: PaymentError[];
}> {
  const errors: PaymentError[] = [];

  // Validate assignment ID
  if (!input.assignmentId || input.assignmentId <= 0) {
    errors.push({
      code: "INVALID_ASSIGNMENT_ID",
      message: "Assignment ID is required and must be positive",
      field: "assignmentId",
    });
  }

  // Validate dates
  if (!input.periodStart) {
    errors.push({
      code: "MISSING_PERIOD_START",
      message: "Period start date is required",
      field: "periodStart",
    });
  }

  if (!input.periodEnd) {
    errors.push({
      code: "MISSING_PERIOD_END",
      message: "Period end date is required",
      field: "periodEnd",
    });
  }

  if (input.periodStart && input.periodEnd) {
    const startDate = new Date(input.periodStart);
    const endDate = new Date(input.periodEnd);

    if (endDate <= startDate) {
      errors.push({
        code: "INVALID_DATE_RANGE",
        message: "Period end date must be after start date",
        field: "periodEnd",
      });
    }
  }

  // Validate payout amount
  if (input.payoutAmount < 0) {
    errors.push({
      code: "NEGATIVE_PAYOUT_AMOUNT",
      message: "Payout amount cannot be negative",
      field: "payoutAmount",
    });
  }

  // Validate payment method
  if (!input.paymentMethod) {
    errors.push({
      code: "MISSING_PAYMENT_METHOD",
      message: "Payment method is required",
      field: "paymentMethod",
    });
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}
