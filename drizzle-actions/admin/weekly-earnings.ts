"use server";

import { db } from "../../db";
import { eq, desc, and, gte, lte, sum, sql } from "drizzle-orm";
import { getCurrentUserNoCache } from "../../lib/amplifyServerUtils";
import {
  h_weekly_earnings,
  h_driver_debt_tracking,
  h_assignment_deposits,
  h_payment_audit_log,
} from "../../drizzle/h_schema/payment-contract";
import { h_assignments } from "../../drizzle/h_schema/assignments";
import { party } from "../../drizzle/schema";
import {
  WeeklyEarningsRecord,
  DebtRecord,
  PaymentError,
  WeeklyEarningsSummary,
} from "../../types/payment-contract";

// Define local types that match the actual database schema
interface WeeklyEarningsInput {
  assignmentId: number;
  weekStartDate: string;
  weekEndDate?: string;
  grossEarnings: number;
  totalEarnings: number;
  platformEarnings?: Record<string, number>;
  fuelCosts?: number;
  otherExpenses?: number;
  platformName?: "uber" | "bolt" | "indriver" | "other";
  weeklyTarget?: number;
  notes?: string;
}



// =====================================================
// WEEKLY EARNINGS RECORDING
// =====================================================

/**
 * Record weekly earnings for a driver assignment
 * ✅ FIXED: Now works with h_assignments bridge and proper schema
 */
export async function recordWeeklyEarnings(
  input: WeeklyEarningsInput
): Promise<{
  success: boolean;
  data?: WeeklyEarningsRecord;
  debtCreated?: DebtRecord;
  error?: PaymentError;
}> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      return {
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Admin authentication required",
        },
      };
    }

    console.log("🔍 [recordWeeklyEarnings] Recording earnings for assignment:", input.assignmentId);

    // Parse dates
    const weekStart = new Date(input.weekStartDate);
    const weekEnd = input.weekEndDate ? new Date(input.weekEndDate) : new Date(weekStart.getTime() + 6 * 24 * 60 * 60 * 1000);

    // Set default values
    const weeklyTarget = input.weeklyTarget || 2700;
    const grossEarnings = input.totalEarnings || input.grossEarnings;
    const shortfall = Math.max(0, weeklyTarget - grossEarnings);

    // Insert weekly earnings record
    const [earningsRecord] = await db
      .insert(h_weekly_earnings)
      .values({
        assignmentId: input.assignmentId,
        weekStartDate: weekStart.toISOString().split('T')[0], // Convert to date string
        weekEndDate: weekEnd.toISOString().split('T')[0],     // Convert to date string
        grossEarnings: grossEarnings.toString(),              // Convert to decimal string
        platformName: input.platformName || "uber",           // Use default "uber" not "other"
        weeklyTarget: weeklyTarget.toString(),                // Convert to decimal string
        earningsShortfall: shortfall.toString(),              // Convert to decimal string
        recordedBy: adminPartyId,
        notes: input.notes,
      })
      .returning();

    console.log("✅ [recordWeeklyEarnings] Created earnings record:", earningsRecord.id);

    // Create debt record if there's a shortfall
    let debtRecord = null;
    if (shortfall > 0) {
      const [debt] = await db
        .insert(h_driver_debt_tracking)
        .values({
          assignmentId: input.assignmentId,
          debtSourceId: earningsRecord.id,                    // Reference to the earnings record
          debtSourceType: "earnings_shortfall",              // Source type enum
          debtAmount: shortfall.toString(),                   // Convert to decimal string
          runningBalance: shortfall.toString(),               // Initial running balance
          transactionDate: new Date(),
          notes: `Shortfall from week ${weekStart.toISOString().split('T')[0]} - ${weekEnd.toISOString().split('T')[0]}`,
        })
        .returning();

      debtRecord = debt;
      console.log("⚠️ [recordWeeklyEarnings] Created debt record for shortfall:", shortfall);
    }

    // Create audit log
    await db.insert(h_payment_audit_log).values({
      assignmentId: input.assignmentId,
      actionType: "RECORD_WEEKLY_EARNINGS",
      tableName: "h_weekly_earnings",
      recordId: earningsRecord.id,
      newValues: {
        weekStart: weekStart.toISOString().split('T')[0],
        weekEnd: weekEnd.toISOString().split('T')[0],
        grossEarnings,
        shortfall,
        platform: input.platformName,
        debtCreated: shortfall > 0,
      },
      performedBy: adminPartyId,
    });

    return {
      success: true,
      data: {
        id: earningsRecord.id,
        assignmentId: input.assignmentId,
        weekStartDate: weekStart.toISOString().split('T')[0],
        weekEndDate: weekEnd.toISOString().split('T')[0],
        grossEarnings,
        weeklyTarget,
        earningsShortfall: shortfall,
        platformName: input.platformName || "uber",
        notes: input.notes,
        recordedBy: adminPartyId,
      } as WeeklyEarningsRecord,
      debtCreated: debtRecord as DebtRecord,
    };
  } catch (error) {
    console.error("❌ [recordWeeklyEarnings] Error:", error);
    return {
      success: false,
      error: {
        code: "DATABASE_ERROR",
        message: error instanceof Error ? error.message : "Failed to record weekly earnings",
      },
    };
  }
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      return {
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Admin authentication required",
        },
      };
    }

    // Calculate week dates (assuming week starts on Monday and ends on Sunday)
    const weekStart = new Date(input.weekStartDate);
    const weekEnd = new Date(weekStart);
    weekEnd.setDate(weekStart.getDate() + 6); // Add 6 days to get Sunday

    // Check for existing earnings record for this week
    const existingRecord = await db
      .select()
      .from(h_weekly_earnings)
      .where(
        and(
          eq(h_weekly_earnings.assignmentId, input.assignmentId),
          eq(h_weekly_earnings.weekStartDate, weekStart)
        )
      )
      .limit(1);

    if (existingRecord.length > 0) {
      return {
        success: false,
        error: {
          code: "DUPLICATE_EARNINGS_RECORD",
          message: "Earnings record already exists for this week",
          field: "weekStartDate",
        },
      };
    }

    const weeklyTarget = input.weeklyTarget || 2700;
    
    // Calculate shortfall
    const shortfall = Math.max(0, weeklyTarget - input.grossEarnings);
    const hasShortfall = shortfall > 0;

    // Start transaction
    const result = await db.transaction(async (tx) => {
      // Insert weekly earnings record
      const [earningsRecord] = await tx
        .insert(h_weekly_earnings)
        .values({
          assignmentId: input.assignmentId,
          weekStartDate: weekStart,
          weekEndDate: weekEnd,
          grossEarnings: input.grossEarnings,
          platformName: input.platformName || "uber",
          weeklyTarget: weeklyTarget,
          earningsShortfall: shortfall,
          recordedBy: adminPartyId,
          notes: input.notes,
        })
        .returning();

      // Create debt record if there's a shortfall
      let debtRecord: DebtRecord | undefined;
      if (hasShortfall) {
        const [newDebtRecord] = await tx
          .insert(h_driver_debt_tracking)
          .values({
            assignmentId: input.assignmentId,
            sourceType: "earnings_shortfall",
            amount: shortfall,
            description: `Weekly earnings shortfall for week ${weekStart.toISOString().split("T")[0]} to ${weekEnd.toISOString().split("T")[0]}`,
            sourceRecordId: earningsRecord.id,
            sourceWeek: weekStart,
            notes: `Expected: R${weeklyExpectedEarnings}, Actual: R${input.totalEarnings}, Shortfall: R${shortfall}`,
            createdBy: adminPartyId,
          })
          .returning();

        debtRecord = {
          id: newDebtRecord.id,
          assignmentId: newDebtRecord.assignmentId,
          sourceType: newDebtRecord.sourceType,
          amount: newDebtRecord.amount.toString(),
          description: newDebtRecord.description,
          sourceRecordId: newDebtRecord.sourceRecordId || undefined,
          sourceWeek: newDebtRecord.sourceWeek?.toISOString() || undefined,
          isResolved: newDebtRecord.isResolved,
          resolvedAt: newDebtRecord.resolvedAt?.toISOString() || undefined,
          resolvedBy: newDebtRecord.resolvedBy || undefined,
          resolutionNotes: newDebtRecord.resolutionNotes || undefined,
          notes: newDebtRecord.notes || undefined,
          createdAt: newDebtRecord.createdAt.toISOString(),
          createdBy: newDebtRecord.createdBy,
        };
      }

      // Create audit log entry
      await tx.insert(h_payment_audit_log).values({
        assignmentId: input.assignmentId,
        actionType: "RECORD_WEEKLY_EARNINGS",
        tableName: "h_weekly_earnings",
        recordId: earningsRecord.id,
        newValues: {
          weekStart: weekStart.toISOString().split("T")[0],
          weekEnd: weekEnd.toISOString().split("T")[0],
          totalEarnings: input.totalEarnings,
          expectedEarnings: weeklyExpectedEarnings,
          shortfall: shortfall,
          debtCreated: hasShortfall,
        },
        performedBy: adminPartyId,
      });

      const formattedRecord: WeeklyEarningsRecord = {
        id: earningsRecord.id,
        assignmentId: earningsRecord.assignmentId,
        weekStartDate: earningsRecord.weekStartDate.toISOString(),
        weekEndDate: earningsRecord.weekEndDate.toISOString(),
        platformEarnings:
          (earningsRecord.platformEarnings as Record<string, number>) || {},
        totalEarnings: earningsRecord.totalEarnings.toString(),
        fuelCosts: earningsRecord.fuelCosts?.toString() || undefined,
        otherExpenses: earningsRecord.otherExpenses?.toString() || undefined,
        netEarnings: earningsRecord.netEarnings?.toString() || undefined,
        expectedEarnings:
          earningsRecord.expectedEarnings?.toString() || undefined,
        shortfall: earningsRecord.shortfall?.toString() || undefined,
        recordedAt: earningsRecord.recordedAt.toISOString(),
        recordedBy: earningsRecord.recordedBy,
        notes: earningsRecord.notes || undefined,
      };

      return { earningsRecord: formattedRecord, debtRecord };
    });

    return {
      success: true,
      data: result.earningsRecord,
      debtCreated: result.debtRecord,
    };
  } catch (error) {
    console.error("❌ [recordWeeklyEarnings] Error:", error);
    return {
      success: false,
      error: {
        code: "EARNINGS_RECORD_FAILED",
        message:
          error instanceof Error
            ? error.message
            : "Failed to record weekly earnings",
      },
    };
  }
}

/**
 * Update existing weekly earnings record
 */
export async function updateWeeklyEarnings(
  earningsId: number,
  updates: Partial<WeeklyEarningsInput>
): Promise<{
  success: boolean;
  data?: WeeklyEarningsRecord;
  error?: PaymentError;
}> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      return {
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Admin authentication required",
        },
      };
    }

    // Get existing record
    const existingRecords = await db
      .select()
      .from(h_weekly_earnings)
      .where(eq(h_weekly_earnings.id, earningsId))
      .limit(1);

    if (existingRecords.length === 0) {
      return {
        success: false,
        error: {
          code: "EARNINGS_NOT_FOUND",
          message: "Weekly earnings record not found",
        },
      };
    }

    const existingRecord = existingRecords[0];

    // Calculate new values
    const newGrossEarnings =
      updates.grossEarnings !== undefined
        ? updates.grossEarnings
        : Number(existingRecord.grossEarnings);
    const weeklyTarget = Number(existingRecord.weeklyTarget) || 2700;
    const newShortfall = Math.max(0, weeklyTarget - newGrossEarnings);

    // Start transaction
    const result = await db.transaction(async (tx) => {
      // Update earnings record
      const [updatedRecord] = await tx
        .update(h_weekly_earnings)
        .set({
          grossEarnings: newGrossEarnings.toString(),
          platformName: updates.platformName || existingRecord.platformName,
          weeklyTarget: updates.weeklyTarget?.toString() || existingRecord.weeklyTarget,
          earningsShortfall: newShortfall.toString(),
          notes:
            updates.notes !== undefined ? updates.notes : existingRecord.notes,
        })
        .where(eq(h_weekly_earnings.id, earningsId))
        .returning();

      // Update or create debt record for shortfall changes
      const oldShortfall = Number(existingRecord.earningsShortfall) || 0;

      if (oldShortfall !== newShortfall) {
        // Check if there's an existing debt record for this earnings record
        const existingDebt = await tx
          .select()
          .from(h_driver_debt_tracking)
          .where(
            and(
              eq(
                h_driver_debt_tracking.assignmentId,
                existingRecord.assignmentId
              ),
              eq(h_driver_debt_tracking.debtSourceId, earningsId),
              eq(h_driver_debt_tracking.debtSourceType, "earnings_shortfall")
            )
          )
          .limit(1);

        if (existingDebt.length > 0 && newShortfall === 0) {
          // Remove debt record if shortfall is now zero - for now just update the amount to 0
          await tx
            .update(h_driver_debt_tracking)
            .set({
              debtAmount: "0",
              runningBalance: "0",
              notes: "Shortfall resolved by earnings update",
            })
            .where(eq(h_driver_debt_tracking.id, existingDebt[0].id));
        } else if (existingDebt.length > 0 && newShortfall > 0) {
          // Update existing debt record
          await tx
            .update(h_driver_debt_tracking)
            .set({
              debtAmount: newShortfall.toString(),
              notes: `Expected: R${weeklyTarget}, Actual: R${newGrossEarnings}, Shortfall: R${newShortfall} (Updated)`,
            })
            .where(eq(h_driver_debt_tracking.id, existingDebt[0].id));
        } else if (existingDebt.length === 0 && newShortfall > 0) {
          // Create new debt record
          await tx.insert(h_driver_debt_tracking).values({
            assignmentId: existingRecord.assignmentId,
            debtSourceId: earningsId,
            debtSourceType: "earnings_shortfall",
            debtAmount: newShortfall.toString(),
            runningBalance: newShortfall.toString(), // Assuming this is the same as debt amount for new debts
            notes: `Expected: R${weeklyTarget}, Actual: R${newGrossEarnings}, Shortfall: R${newShortfall}`,
          });
        }
      }

      // Create audit log entry
      await tx.insert(h_payment_audit_log).values({
        assignmentId: existingRecord.assignmentId,
        actionType: "UPDATE_WEEKLY_EARNINGS",
        tableName: "h_weekly_earnings",
        recordId: earningsId,
        oldValues: {
          grossEarnings: existingRecord.grossEarnings,
          earningsShortfall: oldShortfall,
        },
        newValues: {
          grossEarnings: newGrossEarnings,
          earningsShortfall: newShortfall,
        },
        performedBy: adminPartyId,
      });

      return updatedRecord;
    });

    const formattedRecord: WeeklyEarningsRecord = {
      id: result.id,
      assignmentId: result.assignmentId,
      weekStartDate: result.weekStartDate.toString(),
      weekEndDate: result.weekEndDate.toString(),
      grossEarnings: Number(result.grossEarnings),
      platformName: result.platformName,
      weeklyTarget: Number(result.weeklyTarget),
      earningsShortfall: Number(result.earningsShortfall),
      recordedAt: new Date().toString(), // Using current time since recordedAt doesn't exist in schema
      recordedBy: result.recordedBy,
      notes: result.notes || undefined,
    };

    return {
      success: true,
      data: formattedRecord,
    };
  } catch (error) {
    console.error("❌ [updateWeeklyEarnings] Error:", error);
    return {
      success: false,
      error: {
        code: "EARNINGS_UPDATE_FAILED",
        message:
          error instanceof Error
            ? error.message
            : "Failed to update weekly earnings",
      },
    };
  }
}

/**
 * Get weekly earnings for a specific assignment and date range
 */
export async function getWeeklyEarnings(
  assignmentId: number,
  startDate?: string,
  endDate?: string
): Promise<{
  success: boolean;
  data?: WeeklyEarningsRecord[];
  error?: PaymentError;
}> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      return {
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Admin authentication required",
        },
      };
    }

    // Build query conditions
    const conditions = [eq(h_weekly_earnings.assignmentId, assignmentId)];

    if (startDate) {
      conditions.push(
        gte(h_weekly_earnings.weekStartDate, startDate)
      );
    }

    if (endDate) {
      conditions.push(lte(h_weekly_earnings.weekEndDate, endDate));
    }

    const earnings = await db
      .select()
      .from(h_weekly_earnings)
      .where(and(...conditions))
      .orderBy(desc(h_weekly_earnings.weekStartDate));

    const formattedEarnings: WeeklyEarningsRecord[] = earnings.map(
      (record) => ({
        id: record.id,
        assignmentId: record.assignmentId,
        weekStartDate: record.weekStartDate.toString().split("T")[0], // YYYY-MM-DD format
        weekEndDate: record.weekEndDate.toString().split("T")[0], // YYYY-MM-DD format
        grossEarnings: Number(record.grossEarnings),
        platformName: record.platformName as "uber" | "bolt" | "indriver" | "other",
        weeklyTarget: Number(record.weeklyTarget),
        earningsShortfall: Number(record.earningsShortfall),
        recordedAt: new Date().toISOString(),
        recordedBy: record.recordedBy,
        notes: record.notes || undefined,
      })
    );

    return {
      success: true,
      data: formattedEarnings,
    };
  } catch (error) {
    console.error("❌ [getWeeklyEarnings] Error:", error);
    return {
      success: false,
      error: {
        code: "FETCH_EARNINGS_FAILED",
        message:
          error instanceof Error
            ? error.message
            : "Failed to fetch weekly earnings",
      },
    };
  }
}

/**
 * Get weekly earnings summary for multiple assignments
 */
export async function getWeeklyEarningsSummary(
  assignmentIds: number[],
  startDate?: string,
  endDate?: string
): Promise<{
  success: boolean;
  data?: WeeklyEarningsSummary[];
  error?: PaymentError;
}> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      return {
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Admin authentication required",
        },
      };
    }

    const summaries = await Promise.all(
      assignmentIds.map(async (assignmentId) => {
        // Build query conditions
        const conditions = [eq(h_weekly_earnings.assignmentId, assignmentId)];

        if (startDate) {
          conditions.push(
            gte(h_weekly_earnings.weekStartDate, startDate)
          );
        }

        if (endDate) {
          conditions.push(
            lte(h_weekly_earnings.weekEndDate, endDate)
          );
        }

        // Get earnings summary
        const summaryQuery = await db
          .select({
            totalEarnings: sum(h_weekly_earnings.grossEarnings),
            totalExpectedEarnings: sum(h_weekly_earnings.weeklyTarget),
            totalShortfall: sum(h_weekly_earnings.earningsShortfall),
            weeksRecorded: sql<number>`COUNT(*)`,
          })
          .from(h_weekly_earnings)
          .where(and(...conditions));

        const summary = summaryQuery[0];

        // Get latest earnings record
        const latestRecord = await db
          .select()
          .from(h_weekly_earnings)
          .where(eq(h_weekly_earnings.assignmentId, assignmentId))
          .orderBy(desc(h_weekly_earnings.weekStartDate))
          .limit(1);

        const totalEarnings = Number(summary.totalEarnings) || 0;
        const totalExpected = Number(summary.totalExpectedEarnings) || 0;
        const totalShortfall = Number(summary.totalShortfall) || 0;
        const weeksRecorded = Number(summary.weeksRecorded) || 0;

        return {
          assignmentId,
          totalEarnings: summary.totalEarnings?.toString() || "0",
          totalExpectedEarnings:
            summary.totalExpectedEarnings?.toString() || "0",
          totalShortfall: summary.totalShortfall?.toString() || "0",
          weeksRecorded,
          averageWeeklyEarnings:
            weeksRecorded > 0
              ? (totalEarnings / weeksRecorded).toString()
              : "0",
          performanceRate:
            totalExpected > 0
              ? (totalEarnings / totalExpected).toString()
              : "0",
          latestWeekDate:
            latestRecord[0]?.weekStartDate.toString() || undefined,
          shortfallPercentage:
            totalExpected > 0
              ? Math.round((totalShortfall / totalExpected) * 100 * 100) / 100
              : 0,
        };
      })
    );

    return {
      success: true,
      data: summaries,
    };
  } catch (error) {
    console.error("❌ [getWeeklyEarningsSummary] Error:", error);
    return {
      success: false,
      error: {
        code: "FETCH_EARNINGS_SUMMARY_FAILED",
        message:
          error instanceof Error
            ? error.message
            : "Failed to fetch earnings summary",
      },
    };
  }
}

/**
 * Delete a weekly earnings record
 */
export async function deleteWeeklyEarnings(
  earningsId: number,
  reason: string
): Promise<{ success: boolean; error?: PaymentError }> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      return {
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Admin authentication required",
        },
      };
    }

    // Get existing record
    const existingRecords = await db
      .select()
      .from(h_weekly_earnings)
      .where(eq(h_weekly_earnings.id, earningsId))
      .limit(1);

    if (existingRecords.length === 0) {
      return {
        success: false,
        error: {
          code: "EARNINGS_NOT_FOUND",
          message: "Weekly earnings record not found",
        },
      };
    }

    const existingRecord = existingRecords[0];

    // Start transaction
    await db.transaction(async (tx) => {
      // Update any associated debt records to zero them out (since the schema doesn't have isResolved fields)
      await tx
        .update(h_driver_debt_tracking)
        .set({
          debtAmount: "0",
          runningBalance: "0",
          notes: `Resolved due to earnings record deletion: ${reason}`,
        })
        .where(
          and(
            eq(h_driver_debt_tracking.debtSourceId, earningsId),
            eq(h_driver_debt_tracking.debtSourceType, "earnings_shortfall")
          )
        );

      // Delete earnings record
      await tx
        .delete(h_weekly_earnings)
        .where(eq(h_weekly_earnings.id, earningsId));

      // Create audit log entry
      await tx.insert(h_payment_audit_log).values({
        assignmentId: existingRecord.assignmentId,
        actionType: "DELETE_WEEKLY_EARNINGS",
        tableName: "h_weekly_earnings",
        recordId: earningsId,
        oldValues: {
          weekStart: existingRecord.weekStartDate.toString().split("T")[0],
          weekEnd: existingRecord.weekEndDate.toString().split("T")[0],
          grossEarnings: existingRecord.grossEarnings,
          earningsShortfall: existingRecord.earningsShortfall || 0,
          deletionReason: reason,
        },
        performedBy: adminPartyId,
      });
    });

    return {
      success: true,
    };
  } catch (error) {
    console.error("❌ [deleteWeeklyEarnings] Error:", error);
    return {
      success: false,
      error: {
        code: "EARNINGS_DELETE_FAILED",
        message:
          error instanceof Error
            ? error.message
            : "Failed to delete weekly earnings record",
      },
    };
  }
}

// =====================================================
// VALIDATION HELPERS
// =====================================================

/**
 * Validate weekly earnings input
 */
export async function validateWeeklyEarnings(input: WeeklyEarningsInput): Promise<{
  isValid: boolean;
  errors: PaymentError[];
}> {
  const errors: PaymentError[] = [];

  // Validate assignment ID
  if (!input.assignmentId || input.assignmentId <= 0) {
    errors.push({
      code: "INVALID_ASSIGNMENT_ID",
      message: "Assignment ID is required and must be positive",
      field: "assignmentId",
    });
  }

  // Validate dates
  if (!input.weekStartDate) {
    errors.push({
      code: "MISSING_WEEK_START_DATE",
      message: "Week start date is required",
      field: "weekStartDate",
    });
  }

  // Validate earnings
  if (input.totalEarnings < 0) {
    errors.push({
      code: "NEGATIVE_EARNINGS",
      message: "Total earnings cannot be negative",
      field: "totalEarnings",
    });
  }

  if (input.fuelCosts && input.fuelCosts < 0) {
    errors.push({
      code: "NEGATIVE_FUEL_COSTS",
      message: "Fuel costs cannot be negative",
      field: "fuelCosts",
    });
  }

  if (input.otherExpenses && input.otherExpenses < 0) {
    errors.push({
      code: "NEGATIVE_OTHER_EXPENSES",
      message: "Other expenses cannot be negative",
      field: "otherExpenses",
    });
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}
