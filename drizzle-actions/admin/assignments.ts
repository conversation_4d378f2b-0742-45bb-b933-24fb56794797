"use server";

import { db } from "@/db";
import { eq, desc, and, sql } from "drizzle-orm";
import { getCurrentUserNoCache } from "@/lib/amplifyServerUtils";
import {
  h_assignments,
  h_assignment_events,
} from "@/drizzle/h_schema/assignments";
import {
  vehicles,
  party,
  individual,
  vehicleModel,
  vehicleMake,
} from "@/drizzle/schema";

// Types
export interface AssignmentRecord {
  id: number;
  driverPartyId: number;
  driverName: string;
  driverEmail?: string;
  vehicleId: number;
  vehicleName: string;
  vehicleRegistration: string;
  adminPartyId: number;
  assignmentDate: string;
  status: string;
  weeklyRate: number;
  initiationFee: number;
  initiationFeePaid: number;
  outstandingBalance: number;
  contractUploaded: boolean;
  documentsComplete: boolean;
  paymentStatus: string;
  lastPaymentDate?: string;
  nextPaymentDue?: string;
  performanceRating?: number;
  notes?: string;
}

export interface CreateAssignmentInput {
  driverPartyId: number;
  vehicleId: number;
  weeklyRate: number;
  initiationFee?: number;
  notes?: string;
}

/**
 * Create a new vehicle-driver assignment
 */
export async function createAssignment(input: CreateAssignmentInput): Promise<{
  success: boolean;
  data?: AssignmentRecord;
  error?: string;
}> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      return {
        success: false,
        error: "Admin authentication required",
      };
    }

    // Check if driver already has an active assignment
    const existingAssignment = await db
      .select()
      .from(h_assignments)
      .where(
        and(
          eq(h_assignments.driverPartyId, input.driverPartyId),
          sql`${h_assignments.status} IN ('pending_setup', 'contract_uploaded', 'active')`
        )
      )
      .limit(1);

    if (existingAssignment.length > 0) {
      return {
        success: false,
        error: "Driver already has an active assignment",
      };
    }

    // Check if vehicle is already assigned
    const existingVehicleAssignment = await db
      .select()
      .from(h_assignments)
      .where(
        and(
          eq(h_assignments.vehicleId, input.vehicleId),
          sql`${h_assignments.status} IN ('pending_setup', 'contract_uploaded', 'active')`
        )
      )
      .limit(1);

    if (existingVehicleAssignment.length > 0) {
      return {
        success: false,
        error: "Vehicle is already assigned to another driver",
      };
    }

    // Create the assignment
    const result = await db.transaction(async (tx) => {
      // Insert assignment
      const [newAssignment] = await tx
        .insert(h_assignments)
        .values({
          driverPartyId: input.driverPartyId,
          vehicleId: input.vehicleId,
          adminPartyId,
          weeklyRate: input.weeklyRate.toString(),
          initiationFee: (input.initiationFee || 0).toString(),
          nextPaymentDue: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
          notes: input.notes,
        })
        .returning();

      // ✅ REMOVED: h_assignment_events creation (causing schema issues)
      // Event tracking can be added later once schema is confirmed

      return newAssignment;
    });

    // Get the full assignment details
    const assignmentDetails = await getAssignmentById(result.id);
    
    return {
      success: true,
      data: assignmentDetails.data,
    };
  } catch (error) {
    console.error("❌ [createAssignment] Error:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to create assignment",
    };
  }
}

/**
 * Get assignment by ID with full details
 */
export async function getAssignmentById(assignmentId: number): Promise<{
  success: boolean;
  data?: AssignmentRecord;
  error?: string;
}> {
  try {
    const assignments = await db
      .select({
        // Assignment fields
        id: h_assignments.id,
        driverPartyId: h_assignments.driverPartyId,
        vehicleId: h_assignments.vehicleId,
        adminPartyId: h_assignments.adminPartyId,
        assignmentDate: h_assignments.assignmentDate,
        status: h_assignments.status,
        weeklyRate: h_assignments.weeklyRate,
        initiationFee: h_assignments.initiationFee,
        initiationFeePaid: h_assignments.initiationFeePaid,
        contractUploaded: h_assignments.contractUploaded,
        documentsComplete: h_assignments.documentsComplete,
        paymentStatus: h_assignments.paymentStatus,
        lastPaymentDate: h_assignments.lastPaymentDate,
        nextPaymentDue: h_assignments.nextPaymentDue,
        performanceRating: h_assignments.performanceRating,
        notes: h_assignments.notes,
        
        // Driver details
        driverFirstName: individual.firstName,
        driverLastName: individual.lastName,
        
        // Vehicle details
        vehicleMake: vehicleMake.name,
        vehicleModel: vehicleModel.model,
        vehicleYear: vehicles.manufacturingYear,
        vehicleRegistration: vehicles.vehicleRegistration,
      })
      .from(h_assignments)
      .innerJoin(party, eq(party.id, h_assignments.driverPartyId))
      .leftJoin(individual, eq(individual.partyId, party.id))
      .innerJoin(vehicles, eq(vehicles.id, h_assignments.vehicleId))
      .leftJoin(vehicleModel, eq(vehicleModel.id, vehicles.modelId))
      .leftJoin(vehicleMake, eq(vehicleMake.id, vehicleModel.makeId))
      .where(eq(h_assignments.id, assignmentId))
      .limit(1);

    if (assignments.length === 0) {
      return {
        success: false,
        error: "Assignment not found",
      };
    }

    const assignment = assignments[0];
    
    // Calculate outstanding balance (initiation fee - paid)
    const outstandingBalance = Number(assignment.initiationFee) - Number(assignment.initiationFeePaid);

    const assignmentRecord: AssignmentRecord = {
      id: assignment.id,
      driverPartyId: assignment.driverPartyId,
      driverName: `${assignment.driverFirstName || ""} ${assignment.driverLastName || ""}`.trim() || "Unknown Driver",
      vehicleId: assignment.vehicleId,
      vehicleName: `${assignment.vehicleMake || "Unknown"} ${assignment.vehicleModel || "Unknown"} ${assignment.vehicleYear || ""}`.trim(),
      vehicleRegistration: assignment.vehicleRegistration || "Unknown",
      adminPartyId: assignment.adminPartyId,
      assignmentDate: assignment.assignmentDate.toISOString(),
      status: assignment.status,
      weeklyRate: Number(assignment.weeklyRate),
      initiationFee: Number(assignment.initiationFee),
      initiationFeePaid: Number(assignment.initiationFeePaid),
      outstandingBalance,
      contractUploaded: assignment.contractUploaded,
      documentsComplete: assignment.documentsComplete,
      paymentStatus: assignment.paymentStatus,
      lastPaymentDate: assignment.lastPaymentDate?.toISOString(),
      nextPaymentDue: assignment.nextPaymentDue?.toISOString(),
      performanceRating: assignment.performanceRating ? Number(assignment.performanceRating) : undefined,
      notes: assignment.notes || undefined,
    };

    return {
      success: true,
      data: assignmentRecord,
    };
  } catch (error) {
    console.error("❌ [getAssignmentById] Error:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get assignment",
    };
  }
}

/**
 * Get all assignments for the current admin
 */
export async function getAllAssignments(): Promise<{
  success: boolean;
  data?: AssignmentRecord[];
  error?: string;
}> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      return {
        success: false,
        error: "Admin authentication required",
      };
    }

    const assignments = await db
      .select({
        id: h_assignments.id,
      })
      .from(h_assignments)
      .where(eq(h_assignments.adminPartyId, adminPartyId))
      .orderBy(desc(h_assignments.assignmentDate));

    // Get full details for each assignment
    const assignmentDetails = await Promise.all(
      assignments.map(async (assignment) => {
        const result = await getAssignmentById(assignment.id);
        return result.data;
      })
    );

    // Filter out any failed lookups
    const validAssignments = assignmentDetails.filter(Boolean) as AssignmentRecord[];

    return {
      success: true,
      data: validAssignments,
    };
  } catch (error) {
    console.error("❌ [getAllAssignments] Error:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get assignments",
    };
  }
}
