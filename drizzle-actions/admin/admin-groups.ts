"use server";

import { db } from "@/db";
import {
  groupMemberRoles,
  groupMemberships,
  groups,
  party,
  groupSharedVehicles,
  partyStatus,
} from "@/drizzle/schema";
import {
  h_assignments,
  h_assignment_events,
} from "@/drizzle/h_schema/assignments";
import { GroupRoleEnum } from "@/types/groups";
import { CompanyPurposeEnum } from "@/types/company";
import { PartyStatusEnum } from "@/types/party";
import { createInitialVehiclePossession } from "../bookings";
import { getCurrentUserNoCache } from "@/lib/amplifyServerUtils";
import { sql, and, eq, or, desc, like } from "drizzle-orm";
import { PossessionType } from "@/types/bookings";

const groupPartyTypeId = 3;

// New Poolly-specific group creation for admin-driver vehicle assignments
export async function createPoollyGroup(
  adminPartyId: number,
  driverPartyId: number,
  vehicleId: number,
  groupName?: string
) {
  try {
    const transaction = await db.transaction(async (tx) => {
      // Create a new party for the group
      const partyResult = await tx
        .insert(party)
        .values({
          partyTypeId: groupPartyTypeId,
        })
        .returning();

      // Create party status
      const partyStatusResult = await tx
        .insert(partyStatus)
        .values({
          partyId: partyResult[0].id,
          status: PartyStatusEnum.ACTIVE,
        })
        .returning();

      // Create the group with a default name if not provided
      const defaultGroupName =
        groupName ||
        `Poolly Vehicle Assignment ${new Date().toISOString().split("T")[0]}`;

      const groupResult = await tx
        .insert(groups)
        .values({
          partyId: partyResult[0].id,
          name: defaultGroupName,
          description: "Poolly managed vehicle assignment group",
          cityId: 1, // Default city - you may want to get this from driver's profile
          countryId: 1, // Default country - you may want to get this from driver's profile
          initialPurpose: CompanyPurposeEnum.EHAILING_DRIVER,
          isManaged: true, // Poolly managed
          createdBy: adminPartyId,
          creator: adminPartyId,
        })
        .returning();

      // Add admin as group member with ADMIN role
      const adminMembershipResult = await tx
        .insert(groupMemberships)
        .values({
          partyId: adminPartyId,
          groupId: groupResult[0].id,
          effectiveFrom: new Date().toISOString(),
          effectiveTo: null,
        })
        .returning();

      const adminRoleResult = await tx
        .insert(groupMemberRoles)
        .values({
          groupId: groupResult[0].id,
          partyId: adminPartyId,
          role: GroupRoleEnum.ADMIN,
          effectiveFrom: new Date().toISOString(),
        })
        .returning();

      // Add driver as group member with MEMBER role
      const driverMembershipResult = await tx
        .insert(groupMemberships)
        .values({
          partyId: driverPartyId,
          groupId: groupResult[0].id,
          effectiveFrom: new Date().toISOString(),
          effectiveTo: null,
        })
        .returning();

      const driverRoleResult = await tx
        .insert(groupMemberRoles)
        .values({
          groupId: groupResult[0].id,
          partyId: driverPartyId,
          role: GroupRoleEnum.MEMBER,
          effectiveFrom: new Date().toISOString(),
        })
        .returning();

      // Share the vehicle with the group
      const groupSharedVehicleResult = await tx
        .insert(groupSharedVehicles)
        .values({
          groupId: groupResult[0].id,
          vehicleId: vehicleId,
          sharedBy: adminPartyId,
          effectiveFrom: new Date().toISOString(),
          isActive: true,
        })
        .returning();

      // ✅ PHASE 3: Create h_assignments record for the new group
      const assignmentResult = await tx
        .insert(h_assignments)
        .values({
          groupId: groupResult[0].id,
          driverPartyId,
          vehicleId,
          adminPartyId,
          status: "active",
          createdAt: new Date(),
          effectiveFrom: new Date(),
        })
        .returning();

      // ✅ REMOVED: h_assignment_events creation (causing schema issues)
      // Event tracking can be added later once schema is confirmed

      const finalResult = {
        party: partyResult[0],
        group: groupResult[0],
        partyStatus: partyStatusResult[0],
        adminMembership: adminMembershipResult[0],
        adminRole: adminRoleResult[0],
        driverMembership: driverMembershipResult[0],
        driverRole: driverRoleResult[0],
        groupSharedVehicle: groupSharedVehicleResult[0],
        assignment: assignmentResult[0], // ✅ Include assignment in result
      };

      console.log("Poolly group created with assignment:", finalResult);
      return finalResult;
    });

    // ADD: Create initial possession for the assigned driver
    try {
      await createInitialVehiclePossession(
        vehicleId,
        driverPartyId, // Driver becomes initial possessor
        adminPartyId, // Admin records this assignment
        PossessionType.RENTER, // Driver is renting the vehicle from platform
        "ADMIN_ASSIGNMENT",
        `Vehicle assigned to driver by admin in group "${transaction.group.name}"`
      );
      console.log(
        `✅ Created initial possession for vehicle ${vehicleId} assigned to driver`
      );
    } catch (error) {
      console.error("❌ Failed to create initial vehicle possession:", error);
      // Don't fail the group creation if possession creation fails
    }

    return {
      success: true,
      data: transaction,
      message: "Poolly group created successfully",
    };
  } catch (error) {
    console.error("Error creating Poolly group:", error);
    return {
      success: false,
      error: "Failed to create Poolly group",
      details: error,
    };
  }
}

// ADD: Function to fetch all created groups for admin verification
export async function getAllAdminGroups() {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      throw new Error("Admin party ID not found");
    }

    // Get all groups created by this admin, especially Poolly managed ones
    const adminGroups = await db
      .select({
        id: groups.id,
        name: groups.name,
        description: groups.description,
        isManaged: groups.isManaged,
        createdAt: groups.createdAt,
        createdBy: groups.createdBy,
        memberCount: sql<number>`cast(count(distinct ${groupMemberships.partyId}) as integer)`,
        vehicleCount: sql<number>`cast(count(distinct ${groupSharedVehicles.vehicleId}) as integer)`,
        // Get driver info for Poolly groups
        driverFirstName: sql<string>`(
          SELECT i.first_name 
          FROM group_memberships gm2
          JOIN party p ON gm2.party_id = p.id
          JOIN individual i ON p.id = i.party_id
          WHERE gm2.group_id = ${groups.id} 
          AND gm2.party_id != ${adminPartyId}
          AND gm2.effective_from <= NOW()
          AND (gm2.effective_to IS NULL OR gm2.effective_to > NOW())
          LIMIT 1
        )`,
        driverLastName: sql<string>`(
          SELECT i.last_name 
          FROM group_memberships gm2
          JOIN party p ON gm2.party_id = p.id
          JOIN individual i ON p.id = i.party_id
          WHERE gm2.group_id = ${groups.id} 
          AND gm2.party_id != ${adminPartyId}
          AND gm2.effective_from <= NOW()
          AND (gm2.effective_to IS NULL OR gm2.effective_to > NOW())
          LIMIT 1
        )`,
      })
      .from(groups)
      .leftJoin(
        groupMemberships,
        and(
          eq(groups.id, groupMemberships.groupId),
          sql`${groupMemberships.effectiveFrom} <= NOW()`,
          or(
            sql`${groupMemberships.effectiveTo} IS NULL`,
            sql`${groupMemberships.effectiveTo} > NOW()`
          )
        )
      )
      .leftJoin(
        groupSharedVehicles,
        and(
          eq(groups.id, groupSharedVehicles.groupId),
          eq(groupSharedVehicles.isActive, true)
        )
      )
      .where(eq(groups.createdBy, adminPartyId))
      .groupBy(
        groups.id,
        groups.name,
        groups.description,
        groups.isManaged,
        groups.createdAt,
        groups.createdBy
      )
      .orderBy(desc(groups.createdAt));

    return {
      success: true,
      data: adminGroups.map((group) => ({
        id: group.id,
        name: group.name,
        description: group.description,
        isManaged: group.isManaged,
        createdAt: group.createdAt,
        memberCount: group.memberCount,
        vehicleCount: group.vehicleCount,
        driverName:
          group.driverFirstName && group.driverLastName
            ? `${group.driverFirstName} ${group.driverLastName}`
            : "No driver assigned",
        type: group.isManaged ? "Poolly Managed" : "Regular Group",
      })),
      message: "Admin groups retrieved successfully",
    };
  } catch (error) {
    console.error("Error fetching admin groups:", error);
    return {
      success: false,
      error: "Failed to fetch admin groups",
      details: error,
    };
  }
}

// ADD: Function to search for specific group by name (for debugging)
export async function searchGroupsByName(searchTerm: string) {
  try {
    const groupsResult = await db
      .select({
        id: groups.id,
        name: groups.name,
        description: groups.description,
        isManaged: groups.isManaged,
        createdAt: groups.createdAt,
      })
      .from(groups)
      .where(
        or(
          like(groups.name, `%${searchTerm}%`),
          like(groups.description, `%${searchTerm}%`)
        )
      )
      .orderBy(desc(groups.createdAt));

    return {
      success: true,
      data: groupsResult,
      message: `Found ${groupsResult.length} groups matching "${searchTerm}"`,
    };
  } catch (error) {
    console.error("Error searching groups:", error);
    return {
      success: false,
      error: "Failed to search groups",
      details: error,
    };
  }
}
